# WuStart 抖音自动化工具 - 业务逻辑提取

本项目已成功从原始的 `index.js` 文件中提取出完整的业务逻辑，并重构为模块化的架构。

## 项目结构

```
src/
├── main.js                 # 主入口文件
├── App.vue                 # Vue主组件（已存在）
└── utils/                  # 业务逻辑模块
    ├── app-manager.js      # 应用主管理器
    ├── license.js          # 卡密验证管理
    ├── account.js          # 账号管理
    ├── proxy.js            # 代理IP管理
    ├── wechat.js           # 微信通知
    ├── automation.js       # 自动化任务控制
    ├── statistics.js       # 数据统计
    └── config.js           # 配置管理和工具函数
```

## 核心功能模块

### 1. 卡密验证系统 (`license.js`)
- **功能**: 处理软件授权验证
- **主要特性**:
  - 卡密激活和验证
  - 心跳检查机制（10秒间隔）
  - 设备ID绑定
  - 自动失效处理
- **关键方法**:
  - `activateLicense()` - 激活卡密
  - `checkHeartbeat()` - 心跳检查
  - `startHeartbeat()` - 启动心跳监控

### 2. 账号管理系统 (`account.js`)
- **功能**: 管理抖音账号信息和配置
- **主要特性**:
  - 账号增删改查
  - 批量配置管理
  - 运行时间段控制
  - 任务统计跟踪
- **关键方法**:
  - `addAccount()` - 添加账号
  - `batchConfigAccounts()` - 批量配置
  - `isInAllowedTimeRange()` - 时间段检查

### 3. 代理IP管理 (`proxy.js`)
- **功能**: 管理代理服务器配置
- **主要特性**:
  - 支持HTTP和SOCKS5协议
  - 批量导入代理
  - 代理配置验证
  - 格式化代理列表
- **关键方法**:
  - `addProxy()` - 添加代理
  - `batchAddProxies()` - 批量添加
  - `validateProxy()` - 配置验证

### 4. 微信通知系统 (`wechat.js`)
- **功能**: 企业微信消息推送
- **主要特性**:
  - 企业微信API集成
  - 访问令牌自动管理
  - 多种消息类型支持
  - 连接测试功能
- **关键方法**:
  - `sendMessage()` - 发送消息
  - `sendAccountStatus()` - 账号状态通知
  - `sendDailyStats()` - 每日统计推送

### 5. 自动化任务控制 (`automation.js`)
- **功能**: 控制账号自动化任务执行
- **主要特性**:
  - 账号启停控制
  - 批量操作支持
  - 全局暂停/恢复
  - 任务监控和状态管理
- **关键方法**:
  - `startAccount()` - 启动账号任务
  - `batchStartAccounts()` - 批量启动
  - `toggleGlobalPause()` - 全局暂停切换

### 6. 数据统计系统 (`statistics.js`)
- **功能**: 收集和分析运行数据
- **主要特性**:
  - 实时数据统计
  - 历史数据管理
  - 图表数据生成
  - 效率指标计算
- **关键方法**:
  - `updateAccountStats()` - 更新账号统计
  - `getChartData()` - 获取图表数据
  - `calculateEfficiency()` - 计算效率指标

### 7. 配置管理系统 (`config.js`)
- **功能**: 应用配置和工具函数
- **主要特性**:
  - 分层配置管理
  - 配置验证和导入导出
  - 常用工具函数集合
  - 数据格式验证
- **关键方法**:
  - `loadConfig()` - 加载配置
  - `validateConfig()` - 配置验证
  - 各种工具函数（时间格式化、数据验证等）

### 8. 应用主管理器 (`app-manager.js`)
- **功能**: 统一管理所有业务模块
- **主要特性**:
  - 模块间协调
  - 统一的API接口
  - 事件处理和状态管理
  - 数据导入导出
- **关键方法**:
  - `initialize()` - 应用初始化
  - `getAppStatus()` - 获取应用状态
  - `exportData()` - 数据导出

## 技术特点

### 模块化设计
- 每个功能模块独立封装
- 清晰的职责分离
- 便于维护和扩展

### 异步处理
- 全面使用 async/await
- 错误处理机制完善
- 非阻塞操作设计

### 数据持久化
- 基于 Electron Store 的数据存储
- 自动保存机制
- 数据备份和恢复

### 事件驱动
- 主进程与渲染进程通信
- 实时状态更新
- 响应式数据绑定

## 使用方式

### 在Vue组件中使用

```javascript
import { useAppManager } from '@/utils/app-manager.js'

export default {
  setup() {
    const { appManager, init, getStatus } = useAppManager()
    
    // 初始化应用
    onMounted(async () => {
      await init()
    })
    
    // 获取应用状态
    const status = getStatus()
    
    return {
      appManager,
      status
    }
  }
}
```

### 直接使用管理器

```javascript
import { appManager } from '@/utils/app-manager.js'

// 激活卡密
await appManager.activateLicense('your-license-key')

// 添加账号
await appManager.addAccount({
  id: 'douyin-id',
  ips: 'proxy-config',
  startTime: '08:00',
  endTime: '24:00'
})

// 启动账号
await appManager.startAccount(0)
```

## 数据流程

1. **初始化阶段**:
   - 加载配置文件
   - 验证卡密状态
   - 加载账号和代理数据
   - 初始化统计系统

2. **运行阶段**:
   - 账号任务执行
   - 实时数据统计
   - 状态监控和通知
   - 自动化控制逻辑

3. **数据同步**:
   - 定期保存数据
   - 主进程通信
   - 微信消息推送
   - 统计数据更新

## 配置说明

### 卡密配置
- 支持在线验证
- 心跳检查机制
- 设备绑定验证

### 账号配置
- 运行时间段设置
- 任务数量配置
- 代理IP绑定

### 代理配置
- HTTP/SOCKS5协议
- 批量导入支持
- 连接验证

### 微信通知配置
- 企业微信集成
- 消息模板定制
- 推送频率控制

## 注意事项

1. **数据安全**: 所有敏感数据都经过加密存储
2. **错误处理**: 完善的异常捕获和错误恢复机制
3. **性能优化**: 异步操作和资源管理优化
4. **扩展性**: 模块化设计便于功能扩展

## 后续开发

基于这个业务逻辑架构，可以：
- 继续完善Vue界面组件
- 添加新的自动化功能
- 优化性能和用户体验
- 集成更多第三方服务

所有核心业务逻辑已经完整提取并模块化，可以在此基础上继续开发完整的应用程序。