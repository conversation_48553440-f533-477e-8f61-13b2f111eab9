"use strict"; 

function e(e) { 
  const { app: t, BrowserWindow: n, shell: o, Menu: i, ipcMain: s, session: a, dialog: r, screen: c, net: l, clipboard: d, webContents: u, protocol: w, path: f, os: p, fs: g, store: h, win: y, preload: m, indexHtml: I, iconPath: b, publicKey: x, require: S, __dirname: v, zhufuyu: P, UserAgent: D, keyboard: M, Key: T, pauseAutomation: k, resumeAutomation: C, waitIfPaused: _, initWindowPauseState: O, destroyWindowPauseState: j, setWinStep: R, getWinStep: E, delWinStep: A, getOrCreateProxy: N, releaseProxy: L, machineIdSync: $, clearInterval: F, clearTimeout: H, Buffer: U, console: q, process: z, fetch: B, global: J, crypto: W, CryptoJS: K } = e; 

  function V(e, t) { 
    const n = U.from(e, "base64"); 
    return W.publicDecrypt({ key: t, padding: W.constants.RSA_PKCS1_PADDING }, n).toString("utf8"); 
  } 

  e._intervals = [], e._timeouts = [], e._listeners = [], e.exports = e.exports || {}, 
  e.safeSetInterval = function (t, n) { 
    const o = setInterval(t, n); 
    return e._intervals.push(o), o; 
  }, 
  e.safeSetTimeout = function (t, n) { 
    const o = setTimeout(t, n); 
    return e._timeouts.push(o), o; 
  }, 
  e.safeIpcOn = function (t, n) { 
    e.ipcMain.on(t, n), e._listeners.push({ event: t, handler: n }); 
  }, 
  e.safeIpcHandle = function (t, n) { 
    e.ipcMain.handle(t, n), e._listeners.push({ event: t, handler: n, handle: true }); 
  }, 

  e.exports.selfDestruct = function () { 
    e._intervals.forEach(F), e._timeouts.forEach(H), e._listeners.forEach(({ event: t, handler: n, handle: o }) => { 
      o ? e.ipcMain.removeHandler(t) : e.ipcMain.removeListener(t, n); 
    }), e._intervals = [], e._timeouts = [], e._listeners = [], e.windowList && (e.windowList.length = 0); 
  }, 

  e.safeSetInterval(() => { 
    const n = Date.now(); 
    Date.now() - n > 100 && async function () { 
      Object.keys(J).forEach(e => { 
        try { J[e] = null; } catch (e) {} 
      }), "object" == typeof e && Object.keys(e).forEach(t => { 
        e[t] = null; 
      }); 
      try { t.quit && t.quit(); } catch (e) {} 
      try { Object.keys(S.cache).forEach(e => { delete S.cache[e]; }); } catch (e) {} 
      z.exit(1); 
    }(); 
  }, 5e3); 

  let Y = 0; 
  async function G() { 
    var e = h.get("kami_v1"); 
  } 

  e.safeSetInterval(() => { 
    G().catch(() => {}); 
  }, 6e4), G().catch(() => {}); 

  const X = [new Date("2025-08-07T00:00:00"), new Date("2025-08-11T00:00:00"), new Date("2025-08-12T00:00:00"), new Date("2025-08-15T00:00:00"), new Date("2025-08-18T00:00:00"), new Date("2025-08-20T00:00:00")]; 

  function Q(e, n) { } 
  function Z(e) { } 

  p.release().startsWith("6.1") && t.disableHardwareAcceleration(), "win32" === z.platform && t.setAppUserModelId(t.getName()), t.requestSingleInstanceLock() || (t.quit(), z.exit(0)); 

  let ee = [], te = false; 
  const ne = []; 

  async function oe() { 
    if (!te && 0 !== ne.length) { 
      te = true; 
      try { 
        const e = ne.shift(); 
        e && (await e()); 
      } finally { 
        te = false, e.safeSetTimeout(oe, 100); 
      } 
    } 
  } 

  let ie = null; 

  function se(e) { 
    let t = (h.get("accountList") || []).find(t => t.id === e); 
    if (!t) return false; 
    ie = t; 
    const n = function (e, t, n = new Date()) { 
      if ("object" == typeof ie && ie) { 
        let o = Array.isArray(ie.runHours) && ie.runHours.length > 0 ? ie.runHours : function (e, t) { 
          const [n] = e.split(":").map(Number); 
          let [o] = t.split(":").map(Number); 
          0 === o && (o = 24); 
          let i = []; 
          if (n < o) for (let e = n; e < o; e++) i.push(e);
          else { 
            for (let e = n; e < 24; e++) i.push(e); 
            for (let e = 0; e < o; e++) i.push(e); 
          } 
          return i; 
        }(e, t); 
        const i = n.getHours(); 
        return o.includes(i); 
      } 
      const [o, i] = e.split(":").map(Number); 
      let [s, a] = t.split(":").map(Number); 
      24 === s && (s = 0); 
      const r = 60 * n.getHours() + n.getMinutes(), c = 60 * o + i, l = 60 * s + a; 
      return c < l ? r >= c && r < l : r >= c || r < l; 
    }(t.startTime || "08:00", t.endTime || "24:00"); 
    return ie = null, n; 
  }

  function ae(e) { 
    const t = new Date(), n = t.getFullYear() + "-" + String(t.getMonth() + 1).padStart(2, "0") + "-" + String(t.getDate()).padStart(2, "0"); 
    if (!e.options.lastResetDate) return q.log("first-lastResetDate"), e.options.lastResetDate = n, void (e.options.todayStartFans = e.options.fensiNum); 
    e.options.lastResetDate !== n && (q.log("first-lastResetDate2"), e.options.tongjiNum && Object.keys(e.options.tongjiNum).forEach(t => { 
      e.options.tongjiNum[t] && "object" == typeof e.options.tongjiNum[t] && (e.options.tongjiNum[t].cur = 0); 
    }), q.log("first-lastResetDate3"), e.options.todayStartFans = e.options.fensiNum, e.options.zuotianFans = e.options.jintianFans, e.options.jintianFans = 0, e.options.lastResetDate = n, e.bandDianzanNum = 0, e.bandShoucanNum = 0, e.bandPinglunNum = 0); 
  } 

  function re(e, t) { 
    return Math.floor(e + Math.random() * (t - e + 1)); 
  } 

  async function ce(t, n) { 
    !async function (e) { 
      ne.push(e), oe(); 
    }(async () => { 
      const n = ee.find(e => e.id === t); 
      if (!n) return void q.log("meta not found for id", t); 
      if (n.win.isDestroyed()) return void q.log("win is destroyed for id", t); 
      const o = n.win; 
      function i() { o.focus(), o.show(); } 
      i(); 
      var s = P[Math.floor(Math.random() * P.length)]; 
      d.writeText(s), await new Promise(t => e.safeSetTimeout(t, re(700, 1e3))), i(), await M.pressKey(T.LeftControl, T.V), await M.releaseKey(T.LeftControl, T.V), await new Promise(t => e.safeSetTimeout(t, re(700, 1e3))), i(), await M.pressKey(T.Enter), await M.releaseKey(T.Enter), await new Promise(t => e.safeSetTimeout(t, 350)); 
    }); 
  }

  e.safeSetInterval(() => {
    q.log("min-check");
    const e = h.get("accountList") || [];
    for (const t of e) {
      ie = t;
      t.startTime, t.endTime;
      const e = se(t.id), n = ee.find(e => e.id === t.id);
      n && (!e || n.row.isPaused || n.row.isPopup || (n.row.options.status = "在设置运行范围时间内，开始恢复", C(t.id)), e || (n.row.options.status = "不在设置运行范围时间内", k(t.id)));
    }
    h.set("accountList", e), ie = null;
    for (let e = 0; e < X.length; e++) Q(e, y);
  }, 12e4),

  // IPC事件处理器
  e.safeIpcHandle("gender-check", async (e, { gender: t }) => {
    q.log(t, "gender");
  }),
  e.safeIpcHandle("dzs-check", async (e, { dzs: t }) => {
    q.log(t, "dzs");
  }),
  e.safeIpcHandle("login-check", async (e, { isL: t }) => {
    q.log(t, "isL");
  }),
  e.safeIpcHandle("searchUrl-check", async (e, { url: t }) => {
  }),
  e.safeIpcHandle("account-row-action", async (e, { action: t, id: n, newRow: o }) => {
    const i = "accountList";
    let s = h.get(i) || [];
    const a = ee.find(e => e.id === n);
    if ("get" === t) {
      const e = s.findIndex(e => e.id === n);
      return -1 !== e ? s[e] : null;
    }
    if ("set" === t) {
      a && (a.row = { ...a.row, ...o });
      const e = s.findIndex(e => e.id === n);
      -1 !== e && (s[e] = { ...s[e], ...o }, h.set(i, s));
      !function (e) {
        let t = false;
        for (const n of e) {
          const e = n.options.lastResetDate;
          ae(n), n.options.lastResetDate !== e && (t = true);
        }
        t && (q.log("reset-set"), h.set("accountList", e));
      }(h.get(i) || []);
      return a && a.win.webContents.send("account-row-updated", { id: n, newRow: o }), true;
    }
    return null;
  }),
  e.safeIpcHandle("get-account-row", (e, t) => {
    const n = ee.find(e => e.id === t);
    return n ? n.row : null;
  }),
  e.safeIpcHandle("update-account-row", async (e, { id: t, newRow: n }) => {
    const o = ee.find(e => e.id === t);
    o && (o.row = { ...o.row, ...n });
    const i = "accountList";
    let s = h.get(i) || [];
    const a = s.findIndex(e => e.id === t);
    return -1 !== a && (s[a] = { ...s[a], ...n }, h.set(i, s)), o.win && (o.win.webContents.send("account-row-updated", { id: t, newRow: n }), q.log("up0000000", JSON.stringify(n))), true;
  }),
  e.safeIpcHandle("pasteComment", async (e, t) => {
    await ce(t.id, t.comment);
  }),

  e.safeIpcOn("pauseAutomationDueToPopup", async (e, { id: t, isNotSend: n }) => {
    if (se(t)) {
      const e = ee.find(e => e.id === t), o = h.get("wxNotifyConfig");
      if (o && "object" == typeof o && o.enabled && (q.log(t, n, "id, isNotSend"), e && !n)) {
        await pe("http://***************/pc/send_emil.php", {
          name: "万粉项目",
          emailTo: o.qqEmail,
          deviceId: o.machineId,
          windowId: e.row.index,
          licenseKey: h.get("kami_v1"),
          vesion: 3
        });
      }
    } else q.log("time-no-stop--");
  }),

  e.safeIpcOn("resumeAutomationDueToPopup", async (e, t) => {
    if (se(t)) {
      q.log("start---");
      const e = ee.find(e => e.id === t);
      if (e) {
        e.row.isPopup = false;
        const n = "accountList";
        let o = h.get(n) || [];
        const i = o.find(e => e.id === t);
        i.isPopup = false, h.set(n, o);
      }
      C(t);
    } else q.log("time-no-start--");
  });

  e.safeSetInterval(async () => {
    const t = Date.now();
    for (const n of ee) {
      if (n.row._isRecovering) continue;
      const o = "accountList";
      const i = (h.get(o) || []).find(e => e.id === n.row.id), s = i.options?.lastOprateTime || 0;
      if (!J.onlyBaiduProxy && t - s > 3e4) {
        if (q.log("chaoshi"), n && !n.row.isPaused && !n.row.isPopup && se(n.row.id)) {
          try {
            await n.win.loadURL("https://www.douyin.com");
          } catch (e) {
            q.error("loadURL failed:", e);
          }
          q.log(n.row.id, "ididid"), n.row._isRecovering = true, C(n.row.id), !n || n.row.isPaused || n.row.isPopup || (q.log("20sec-stop-now-start"), 0 == n.row.runMode ? (q.log("yunxingMode0"), be(n.win, n.row, true)) : 1 == n.row.runMode && (q.log("yunxingMode1"), xe(n.win, n.row)));
        }
        e.safeSetTimeout(() => {
          n.row._isRecovering = false, n.row.options.lastOprateTime = Date.now();
        }, 5e3);
      }
    }
  }, 1e4);

  const le = 350, de = 10;

  function ue(e = 0) {
    ee.sort((e, t) => (e.row.index ?? 0) - (t.row.index ?? 0));
    const { width: t, height: n } = c.getPrimaryDisplay().workAreaSize, o = ee.length;
    if (0 === o) return;
    const i = Math.floor((t + de) / 360), s = 2 * i, a = Math.max(450, Math.floor((n - de) / 2));
    if (o <= s) {
      let e = [Math.min(o, i), o > i ? o - i : 0];
      ee.forEach((o, i) => {
        let s = 0, r = i;
        i >= e[0] && (s = 1, r = i - e[0]);
        const c = le, l = a, d = 360 * r, u = s * (a + de), w = Math.min(d, t - c), f = Math.min(u, n - l);
        o.win.isDestroyed() || (o.win.setBounds({ x: w, y: f, width: c, height: l }), o.win.setTitle(o.id), o.win.show());
      });
    } else {
      const e = Math.ceil(o / 2), i = [e, o - e], s = e => {
        if (e <= 1) return 0;
        const n = t - le;
        return Math.max(60, Math.floor(n / (e - 1)));
      };
      ee.forEach((e, o) => {
        let r = 0, c = o;
        o >= i[0] && (r = 1, c = o - i[0]);
        const l = i[r], d = c * s(l), u = r * (a + de), w = a, f = Math.min(d, t - 350), p = Math.min(u, n - w);
        e.win.isDestroyed() || (e.win.setBounds({ x: f, y: p, width: 350, height: w }), e.win.setTitle(e.id), e.win.show());
      });
    }
  }

  async function pe(e, t, n = {}) {
    const o = await B(e, {
      method: "POST",
      headers: { "Content-Type": "application/json", ...n },
      body: JSON.stringify(t)
    });
    if (!o.ok) throw new Error(`HTTP error! status: ${o.status}`);
    return await o.json();
  }

  async function ge(e, t) {
    let n;
    try {
      n = await e.webContents.executeJavaScript("!!window.electron");
    } catch (e) {
      n = false;
    }
    if (!n && (await e.reload(), await async function (e) {
      return new Promise(t => {
        const n = () => {
          e.webContents.removeListener("did-finish-load", n), t(null);
        };
        e.webContents.on("did-finish-load", n);
      });
    }(e), n = await e.webContents.executeJavaScript("!!window.electron"), !n)) throw new Error("Preload 注入失败，自动化终止");
    return await e.webContents.executeJavaScript(t);
  }

  // 核心IPC事件处理
  e.safeIpcOn("set-only-baidu-proxy", (e, t) => {
    J.onlyBaiduProxy = t;
  }),

  e.safeIpcOn("clearAllFansProgress", () => {
    q.log("clearAllFansProgress"), h.delete("fansProgress");
  }),

  e.safeIpcOn("open-google-window", async (e, t) => {
    await fe(t);
  }),

  e.safeIpcOn("stop-google-window", async (e, t) => {
    if (se(t.id)) {
      ee.find(e => e.id === t.id).row.isPaused = true, q.log("btn-stop--"), k(t.id);
    } else q.log("btn-stop--time-no");
  }),

  e.safeIpcOn("resume-google-window", async (e, t) => {
    if (se(t.id)) {
      ee.find(e => e.id === t.id).row.isPaused = false, q.log("btn-resume--"), C(t.id);
    } else q.log("btn-resume--time-no");
  }),

  e.safeIpcOn("kami-heartbeat-failed", () => {
    ee.forEach(e => {
      e.win.isDestroyed() || e.win.close();
    });
  }),

  e.safeIpcOn("closeAll", async t => {
    ee.forEach(async t => {
      t.win.isDestroyed() || t.win.close(), await new Promise(t => e.safeSetTimeout(t, 300));
    });
  }),

  e.safeIpcOn("openAll", async (t, n) => {
    for (const t of n) await fe(t), await new Promise(t => e.safeSetTimeout(t, 300));
  }),

  e.safeIpcOn("batchAll", async (e, t) => {
    for (const e of t) await fe(e);
  }),

  e.safeIpcOn("sortAll", async (e, t) => {
    ee.forEach(e => {
      const n = t.find(t => t.id === e.id);
      n && "number" == typeof n.index && (e.row.index = n.index);
    }), ue(0);
  }),

  e.safeIpcOn("deleteAccount", async (e, t) => {
    const n = ee.findIndex(e => e.id === t);
    if (-1 !== n) {
      const e = ee[n];
      await e.win.webContents.executeJavaScript("window.electron.clearAllAccountData()");
      const o = `persist:user-${t}`, i = a.fromPartition(o);
      await async function (e) {
        await e.clearStorageData({
          storages: ["cookies", "localstorage", "websql", "serviceworkers", "cachestorage", "filesystem", "shadercache"]
        }), await e.clearCache();
      }(i), e.win.isDestroyed() || e.win.close(), ee.splice(n, 1);
    }
    A(t);
  }),

  e.safeIpcOn("login-status", (e, t) => {}),

  t.on("second-instance", () => {
    y && (y.isMinimized() && y.restore(), y.focus());
  }),

  e.safeIpcOn("setNotifyConfig", (e, t) => {
    J.notifyConfig = t;
  }),

  e.safeIpcHandle("open-win", (e, t) => {
    new n({
      webPreferences: {
        devTools: false,
        preload: m,
        nodeIntegration: true,
        contextIsolation: false
      }
    });
  });

  // 窗口创建函数
  async function we(e, t) {
    let n = { country: "CN", city: "Shanghai", timezone: "Asia/Shanghai", lat: 31.2304, lon: 121.4737 };
    try {
      const e = await async function (e) {
        const t = await B(`http://ip-api.com/json/${e}?lang=zh-CN`);
        return await t.json();
      }(t);
      "success" === e.status && (n = {
        country: e.country ?? "CN",
        city: e.city ?? "Shanghai",
        timezone: e.timezone ?? "Asia/Shanghai",
        lat: "number" == typeof e.lat ? e.lat : 31.2304,
        lon: "number" == typeof e.lon ? e.lon : 121.4737
      });
    } catch (e) {}
    const o = ["Intel Inc.", "NVIDIA Corporation", "ATI Technologies Inc.", "Apple Inc."],
          i = ["Intel(R) Iris(TM) Plus Graphics 640", "NVIDIA GeForce GTX 1650", "AMD Radeon Pro 560", "Apple M1"],
          s = parseInt(e, 10) % o.length,
          a = parseInt(e, 10) % i.length;
    return {
      userAgent: new D({ deviceCategory: "desktop" }).toString(),
      screen: {
        width: 1366 + Math.floor(300 * Math.random()),
        height: 768 + Math.floor(200 * Math.random())
      },
      timezone: n.timezone || "Asia/Shanghai",
      language: { CN: "zh-CN", US: "en-US", JP: "ja-JP", RU: "ru-RU", DE: "de-DE", FR: "fr-FR" }[n.country] || "en-US",
      geolocation: { lat: n.lat, lon: n.lon },
      deviceMemory: [4, 8, 16][parseInt(e, 10) % 3],
      hardwareConcurrency: [4, 8, 12][parseInt(e, 10) % 3],
      webglVendor: o[s],
      webglRenderer: i[a]
    };
  }

  // 核心窗口创建函数
  async function fe(t) {
    const i = ee.find(e => e.id === t.id);
    if (i && !i.win.isDestroyed()) return void i.win.focus();

    const s = `persist:user-${t.id}`;
    let r = a.fromPartition(s);
    const c = await N(t);
    c ? await r.setProxy({
      proxyRules: `http=127.0.0.1:${c.port};https=127.0.0.1:${c.port};`,
      proxyBypassRules: "<-loopback>"
    }) : await r.setProxy({ proxyRules: "direct://" }),
    await new Promise(t => e.safeSetTimeout(t, 1e3));

    let l = false, d = 0, u = 0;
    const w = new n({
      icon: b,
      width: le,
      height: 450,
      x: 0,
      y: 0,
      webPreferences: {
        nativeWindowOpen: false,
        devTools: false,
        session: r,
        preload: m,
        nodeIntegration: false,
        contextIsolation: true,
        additionalArguments: ["--myflag=superwindow"]
      },
      title: `窗口${t.index}；${t.ips}；抖音：${t.id}`
    });

    w.webContents.on("devtools-opened", () => {
      w.close();
    }), O(t.id), r.webRequest.onCompleted(e => {
      200 === e.statusCode && (u += 1, d = 0, l && u >= 2 && (l = false, C(t.id), q.log("wan--suc--re")));
    }), w.webContents.on("will-navigate", (e, t) => {
      t.startsWith("http") || t.startsWith("https") || (q.log("阻止跳转到不明协议:", t), e.preventDefault());
    }), w.webContents.setWindowOpenHandler(({ url: e }) => e.startsWith("http") || e.startsWith("https") ? { action: "allow" } : (q.log("阻止打开不明协议:", e), { action: "deny" })),
    w.webContents.setWindowOpenHandler(({ url: e }) => e.startsWith("https:") || e.startsWith("http:") ? (o.openExternal(e), { action: "deny" }) : (q.log("Blocked non-http(s) protocol:", e), { action: "deny" })),
    await w.loadURL("about:blank");

    const f = await w.webContents.executeJavaScript("window.electronAPI.getProxyIp()");
    q.log(f, "proxyIp");
    const p = await we(t.id, f);
    w.webContents.send("inject-fingerprint", p), w.webContents.setUserAgent(p.userAgent),
    w.on("page-title-updated", e => {
      e.preventDefault();
      const n = ee.find(e => e.id === t.id), o = `窗口${n.row.index} 近期${n.row.options.fensiNum} 今日 ${n.row.options.jintianFans}`;
      w.setTitle(o);
    }),

    // 加载目标页面
    J.onlyBaiduProxy ? w.loadURL("https://www.baidu.com/s?wd=ip") : w.loadURL("https://www.douyin.com"),
    q.log(`窗口 ${t.id} 开始加载页面: ${J.onlyBaiduProxy ? "百度IP检测" : "抖音首页"}`),

    // 添加到窗口列表
    ee.push({ id: t.id, win: w, row: t }),
    q.log(`窗口已创建: ${t.id}, 当前窗口总数: ${ee.length}`),
    w.on("closed", () => {
      ee = ee.filter(e => e.id !== t.id), ue(0), j(t.id), L(t);
    }), w.webContents.on("did-finish-load", () => {
      w.webContents.send("set-win-id", t.id);
    }), w.webContents.once("did-finish-load", () => {
      if (!J.onlyBaiduProxy) {
        // 延迟启动自动化，确保页面和preload脚本完全加载
        e.safeSetTimeout(async () => {
          try {
            // 恢复自动化状态
            C(t.id);

            // 根据运行模式启动相应的自动化
            if (t.runMode == 0) {
              q.log("启动养号模式自动化:", t.id);
              await be(w, t, true);
            } else if (t.runMode == 1) {
              q.log("启动火力模式自动化:", t.id);
              await xe(w, t);
            }
          } catch (error) {
            q.error("启动自动化失败:", error);
          }
        }, 3000); // 延迟3秒启动
      }
    }), ue(0);
  }

  // 养号模式自动化函数（调用preload中的API）
  async function be(e, t, n = false) {
    if (n && (t._yangjiToken = Symbol(), t._isYangjiRunning = false), t._isYangjiRunning)
      return void q.log(`[yangji] Already running for row.id=${t.id}, skip.`);

    t._isYangjiRunning = true, t._yangjiToken || (t._yangjiToken = Symbol());
    const o = t._yangjiToken;

    try {
      q.log("开始执行养号模式自动化:", t.id);

      // 调用preload中的checkLoginStatus检查登录状态
      const loginResult = await ge(e, `window.electron.checkLoginStatus('${JSON.stringify({ rowId: t.id, id: "home" })}')`);
      if (!loginResult) {
        q.log("登录检查失败，停止自动化");
        return;
      }

      // 执行养号滑动操作序列
      const swipeActions = ["swipe", "swipe", "dianzan", "swipe", "swipe", "pinglun", "swipe", "shoucan"];

      for (let i = 0; i < swipeActions.length && t._yangjiToken === o; i++) {
        await _(t.id); // 等待暂停状态

        const action = swipeActions[i];
        q.log(`执行养号操作 ${i+1}/${swipeActions.length}: ${action}`);

        // 调用preload中的yjSwipe函数
        await ge(e, `window.electron.yjSwipe('${JSON.stringify({ rowId: t.id, type: action })}')`);

        // 随机等待时间
        await new Promise(resolve => e.safeSetTimeout(resolve, re(2000, 5000)));
      }

      q.log("养号模式自动化完成:", t.id);

    } catch (error) {
      q.error("养号模式自动化执行失败:", error);
    } finally {
      if (t._yangjiToken === o) {
        t._isYangjiRunning = false;
      }
    }
  }

  // 火力模式自动化函数（调用preload中的API）
  async function xe(e, t) {
    if (!se(t.id)) {
      q.log("不在运行时间范围内，停止火力模式");
      return;
    }

    try {
      q.log("开始执行火力模式自动化:", t.id);

      // 检查登录状态
      await _(t.id);
      const loginResult = await ge(e, `window.electron.checkLoginStatus('${JSON.stringify({ rowId: t.id, id: t.id })}')`);
      if (!loginResult) {
        q.log("登录检查失败，停止火力模式");
        return;
      }

      // 跳转到搜索页面
      await _(t.id);
      R(t.id, 2); // 设置步骤状态
      const searchResult = await ge(e, `window.electron.goSearchPage(${t.id})`);
      if (!searchResult) {
        q.log("跳转搜索页面失败");
        return;
      }

      // 获取粉丝列表并执行操作
      await _(t.id);
      R(t.id, 3); // 设置步骤状态
      const fansList = await ge(e, `window.electron.goIdPage(${t.id})`);
      if (!fansList || !Array.isArray(fansList)) {
        q.log("获取粉丝列表失败");
        return;
      }

      q.log(`获取到粉丝列表，共 ${fansList.length} 个粉丝`);

      // 对每个粉丝执行操作
      for (let i = 0; i < fansList.length && se(t.id); i++) {
        await _(t.id);

        const fan = fansList[i];
        q.log(`处理粉丝 ${i+1}/${fansList.length}: ${fan}`);

        // 跳转到粉丝页面
        const goFansResult = await ge(e, `window.electron.goFansPage("${fan}")`);
        if (!goFansResult) {
          q.log(`跳转粉丝页面失败: ${fan}`);
          continue;
        }

        await _(t.id);

        // 执行粉丝页面操作
        const operateResult = await ge(e, `window.electron.FansPageOprate('${JSON.stringify({ options: t.options, id: t.id })}')`);
        if (operateResult) {
          q.log(`粉丝页面操作成功: ${fan}`);
        }

        // 随机等待
        await new Promise(resolve => e.safeSetTimeout(resolve, re(1000, 3000)));
      }

      q.log("火力模式自动化完成:", t.id);

    } catch (error) {
      q.error("火力模式自动化执行失败:", error);
    }
  }

  // 定时检查函数
  const Se = new Date("2025-08-10T10:00:00");
  function ve() {
    Pe(), e.safeSetInterval(Pe, 6e5);
  }
  async function Pe() {
    q.log("threeLast");
  }
  !function () {
    const t = new Date();
    if (t >= Se) ve();
    else {
      const n = Se.getTime() - t.getTime();
      e.safeSetTimeout(ve, n);
    }
  }();
}

// 导出函数
Object.defineProperty(exports, "__esModule", { value: true }), exports.injectBusinessLogic = e;
