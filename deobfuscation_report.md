# main.js 反混淆报告

## 概述
成功对 `main.js` 文件进行了反混淆处理，将被生产环境优化的变量名还原为更具可读性的名称。

## 处理内容

### 1. Webpack 模块系统还原
- `_0x4f32ff` → `modules` (模块数组)
- `_0x230811` → `installedModules` (已安装模块缓存)
- `_0x35e4e6` → `__webpack_require__` (模块加载函数)
- `_0x5e873c` → `moduleId` (模块ID)
- `_0x39838d` → `module` (模块对象)

### 2. 工具类模块还原
- `_0x5ea7de` → `utils` (工具类对象)
- `_0x3abdab` → `resources` (Android资源对象)
- `_0x3a8bc5` → `statusBarHeight` (状态栏高度)
- `_0x344bdc` → `popupWindow` (弹窗对象)
- `_0x3ebb94` → `enterTransition` (进入动画)
- `_0x4f5b19` → `exitTransition` (退出动画)

### 3. 屏幕截图相关
- `_0x59ba7d` → `allowButton` (允许按钮)
- `_0x35dd97` → `startButton` (开始按钮)
- `_0x51eeee` → `captureResult` (截图结果)
- `_0x31a0ec` → `fileName` (文件名)
- `_0x471d9e` → `filePath` (文件路径)

### 4. 时间和动画相关
- `_0x1c8947` → `seconds` (秒数)
- `_0xba3db0` → `message` (消息)
- `_0x13181f` → `currentTime` (当前时间)
- `_0x594242` → `displayMessage` (显示消息)

### 5. 贝塞尔曲线相关
- `_0x545352` → `points` (控制点数组)
- `_0x5655d4` → `t` (时间参数)
- `_0x5c2e5d` → `cx` (X轴控制参数)
- `_0x42b938` → `bx` (X轴二次参数)
- `_0x339b73` → `ax` (X轴三次参数)
- `_0x3824e8` → `cy` (Y轴控制参数)
- `_0x5a38e9` → `by` (Y轴二次参数)
- `_0x367671` → `ay` (Y轴三次参数)

### 6. 手势和滑动相关
- `_0x835f2b` → `startX` (起始X坐标)
- `_0x61e5fa` → `startY` (起始Y坐标)
- `_0x14c56` → `endX` (结束X坐标)
- `_0x38e4f1` → `endY` (结束Y坐标)
- `_0x4575e4` → `duration` (持续时间)
- `_0x416b42` → `gesturePoints` (手势点数组)
- `_0x1818cf` → `controlPoints` (控制点数组)

### 7. 配置相关
- `_0x2c4170` → `config` (配置对象)
- `_0x16fb59` → `storage` (存储对象)
- `_0x3600aa` → `defaultSkinType` (默认皮肤类型)
- `_0x4515f1` → `skinType` (皮肤类型)
- `_0x5d5974` → `currentSkinType` (当前皮肤类型)

### 8. 浮动窗口相关
- `_0x7bc3d1` → `floatWindow` (浮动窗口对象)
- `_0x1ac976` → `window` (窗口对象)
- `_0x2d3bf9` → `posX` (X位置)
- `_0x4c003f` → `posY` (Y位置)

### 9. 抖音应用相关
- `_0x38fa0d` → `douyin` (抖音对象)
- `_0x585a7e` → `liteVersion` (极速版版本)
- `_0x590661` → `huoshanVersion` (火山版版本)
- `_0x3d0894` → `dyVersion` (抖音版本)

### 10. 数值转换
将十六进制数值转换为十进制：
- `0xc8` → `200` (毫秒)
- `0x3e8` → `1000` (毫秒)
- `0x7d0` → `2000` (毫秒)
- `0x1f4` → `500` (毫秒)
- `0x64` → `100` (数值)
- `0x32` → `50` (数值)
- `0xd` → `13` (模块索引)

## 主要功能模块

### 1. 工具类 (utils)
包含Android自动化相关的工具函数：
- 状态栏高度获取
- 弹窗创建
- 服务检查
- 屏幕截图
- 贝塞尔曲线计算
- 随机滑动
- 控制台显示

### 2. 配置模块 (config)
包含应用主题和配置管理：
- 主题列表
- 皮肤类型管理
- 颜色配置

### 3. 浮动窗口模块
包含浮动控制窗口的实现：
- 窗口创建和显示
- 动画效果
- 触摸事件处理

### 4. 抖音自动化模块
包含抖音应用的自动化操作：
- 应用启动
- 版本检查
- 视频信息获取
- 用户交互

## 还原效果
- ✅ 核心变量名已成功还原
- ✅ 函数参数名已优化
- ✅ 十六进制数值已转换为十进制
- ✅ 代码可读性大幅提升
- ⚠️ 部分局部变量仍使用生成的名称（如 var_xxxxx）

## 建议
1. 代码现在更易于理解和维护
2. 可以根据具体业务逻辑进一步优化变量名
3. 建议添加适当的注释来说明复杂的业务逻辑
4. 可以考虑将大文件拆分为多个模块以提高可维护性

## 文件信息
- 原始文件大小: 12,586 行
- 处理后文件大小: 12,586 行
- 主要改进: 变量名可读性提升，数值表示优化
