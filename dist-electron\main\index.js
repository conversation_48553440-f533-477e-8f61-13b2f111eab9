import {
    app,
    ipc<PERSON>ain,
    BrowserWindow,
    Menu,
    shell,
    protocol,
    webContents,
    clipboard,
    net as a0_0x591156,
    screen,
    dialog,
    session,
  } from "electron";
  import a0_0x4a0e80 from "node-fetch";
  import { createRequire } from "node:module";
  import { fileURLToPath } from "node:url";
  import a0_0x25b7e1 from "node:path";
  import a0_0x5610a7 from "node:os";
  import a0_0x5f3bf9 from "node:vm";
  import a0_0x53c070 from "fs";
  import "fetch-blob";
  import a0_0x373525 from "crypto-js";
  import "formdata-polyfill";
  import a0_0x4f3194 from "electron-store";
  import a0_0x4939ae from "node:crypto";
  import a0_0x2dbb00 from "user-agents";
  import a0_0x4c0123 from "node-machine-id";
  import { Key, keyboard } from "@nut-tree/nut-js";
  import a0_0x1e4f7b from "proxy-chain";
  import a0_0x5cf976 from "net";
  const curVersion='3.0.0';
// 检查更新函数
async function checkForUpdate() {
    try {
      // 替换为你的版本检查接口
      const response = await a0_0x124f68json('http://160.202.228.242/pc/version.php',{});
      console.log(response)
      const { version, downloadUrl } = response; // 假设接口返回 { version: "x.x.x", downloadUrl: "https://..." }
      console.info(`接口返回最新版本：${version}`);
  
      // 比较版本号
      if (version !== curVersion) {
        // 显示弹窗
        const options = {
          type: 'info',
          title: '发现新版本',
          message: `当前版本：${curVersion}\n最新版本：${version}\n是否前往下载新版本？`,
          buttons: ['立即下载', '稍后更新']
        };
        const { response } = await dialog.showMessageBox(options);
        
        if (response === 0) { // 用户点击“立即下载”
          shell.openExternal(downloadUrl);
        } 
      } else {
        console.info('当前已是最新版本，无需更新');
      }
    } catch (error) {
        console.error('检查更新失败：' + error.message);
    }
  }

  const a0_0x3b5a53 = new Map();
  function a0_0x3c3339(_0x2dce1b) {
    !a0_0x3b5a53["has"](_0x2dce1b) &&
      a0_0x3b5a53["set"](_0x2dce1b, {
        paused: false,
        resumeResolve: null,
        waitPromise: Promise["resolve"](),
      });
  }
  function a0_0x54f2ca(_0x49bd88) {
    const _0x337c17 = a0_0x3b5a53["get"](_0x49bd88);
    if (!_0x337c17) {
      console["warn"]("Window pause state not found: " + _0x49bd88);
      return;
    }
    _0x337c17["paused"] = true;
    if (!_0x337c17["resumeResolve"]) {
      _0x337c17["waitPromise"] = new Promise(
        (_0x45ec76) => (_0x337c17["resumeResolve"] = _0x45ec76),
      );
    }
    console["log"](
      "[PauseManager] pauseAutomation called for winId=" +
        _0x49bd88 +
        ", paused=" +
        _0x337c17["paused"] +
        ", waitPromise=",
      _0x337c17["waitPromise"],
    );
  }
  function a0_0x5e022e(_0x23f44c) {
    const _0x5ed978 = a0_0x3b5a53["get"](_0x23f44c);
    if (!_0x5ed978) {
      console["warn"]("Window pause state not found: " + _0x23f44c);
      return;
    }
    (_0x5ed978["paused"] = false),
      _0x5ed978["resumeResolve"] &&
        (_0x5ed978["resumeResolve"](),
        (_0x5ed978["resumeResolve"] = null),
        (_0x5ed978["waitPromise"] = Promise["resolve"]())),
      console["log"](
        "[PauseManager] resumeAutomation called for winId=" +
          _0x23f44c +
          ", paused=" +
          _0x5ed978["paused"],
      );
  }
  async function a0_0x8c077b(_0x3a03ad) {
    const _0x432b86 = a0_0x3b5a53["get"](_0x3a03ad);
    if (!_0x432b86) {
      console["warn"]("Window pause state not found: " + _0x3a03ad);
      return;
    }
    console["log"](
      "[PauseManager] waitIfPaused called for winId=" +
        _0x3a03ad +
        ", paused=" +
        _0x432b86["paused"],
    ),
      _0x432b86["paused"] &&
        (console["log"](
          "[PauseManager] winId=" + _0x3a03ad + " is paused, waiting...",
        ),
        await _0x432b86["waitPromise"],
        console["log"](
          "[PauseManager] winId=" + _0x3a03ad + " resumed, continue",
        ));
  }
  function a0_0x291dbc(_0x3c100a) {
    a0_0x3b5a53["delete"](_0x3c100a);
  }
  const a0_0x1c89b5 = {};
  function a0_0x5e8148(_0x3481cf, _0x335c00, _0x2cbd36) {
    a0_0x1c89b5[_0x3481cf] = {
      step: _0x335c00,
      fansIndex: _0x2cbd36,
    };
  }
  function a0_0x3a7dd4(_0x1f1252) {
    return (
      a0_0x1c89b5[_0x1f1252] || {
        step: 0x1,
        fansIndex: 0x0,
      }
    );
  }
  function a0_0x52d860(_0x20d08d) {
    delete a0_0x1c89b5[_0x20d08d];
  }
  const a0_0x554df5 = new a0_0x1e4f7b["Server"]({
    port: 0x46a0,
    prepareRequestFunction: () => ({
      upstreamProxyUrl: "socks5://4yce5qtu:<EMAIL>:62222",
    }),
  });
  a0_0x554df5["listen"]()
    ["then"](() => {
      console["log"]("本地 HTTP 代理已启动，端口:", a0_0x554df5["port"]);
    })
    ["catch"]((_0x226f2f) => {
      console["error"]("启动代理失败:", _0x226f2f);
    });
  const a0_0x203b6d = new Map();
  function a0_0x3d7e60(_0x5ec18d) {
    if (!_0x5ec18d["protocol"] || !_0x5ec18d["ips"]) return null;
    const [_0x387430, _0x1a7b1d, _0x597d50, _0x3e4093] =
      _0x5ec18d["ips"]["split"]("|");
    let _0x44b1fd =
      _0x597d50 && _0x3e4093 ? _0x597d50 + ":" + _0x3e4093 + "@" : "";
    return (
      _0x5ec18d["protocol"] + "://" + _0x44b1fd + _0x387430 + ":" + _0x1a7b1d
    );
  }
  async function a0_0x1948d9(_0x144962) {
    function _0x304541(_0x5d67e8, _0x40ad70) {
      const _0x1e07fc = a0_0x5cf976["createServer"]()
        ["once"]("error", () => _0x304541(_0x5d67e8 + 0x1, _0x40ad70))
        ["once"]("listening", () =>
          _0x1e07fc["close"](() => _0x40ad70(_0x5d67e8)),
        )
        ["listen"](_0x5d67e8);
    }
    return new Promise((_0x4c7646) => _0x304541(_0x144962, _0x4c7646));
  }
  let a0_0x2a9b50 = 0x46a0;
  async function a0_0x17a25f(_0xe89cf3) {
    const _0x5566c5 = a0_0x3d7e60(_0xe89cf3);
    if (!_0x5566c5) return null;
    if (a0_0x203b6d["has"](_0x5566c5)) {
      const _0xe56351 = a0_0x203b6d["get"](_0x5566c5);
      return (_0xe56351["refCount"] += 0x1), _0xe56351;
    }
    const _0x42b240 = await a0_0x1948d9(a0_0x2a9b50);
    a0_0x2a9b50 = _0x42b240 + 0x1;
    const _0x559455 = new a0_0x1e4f7b["Server"]({
      port: _0x42b240,
      prepareRequestFunction: () => ({
        upstreamProxyUrl: _0x5566c5,
      }),
    });
    try {
      await _0x559455["listen"]();
    } catch (_0x131efd) {
      console["error"]("ProxyChain listen error:", _0x131efd);
      throw _0x131efd;
    }
    const _0x16e7b0 = {
      server: _0x559455,
      port: _0x42b240,
      upstream: _0x5566c5,
      refCount: 0x1,
    };
    return a0_0x203b6d["set"](_0x5566c5, _0x16e7b0), _0x16e7b0;
  }
  async function a0_0x3cac28(_0x55d134) {
    const _0x2ee5d7 = a0_0x3d7e60(_0x55d134);
    if (!_0x2ee5d7) return;
    const _0x34ec3a = a0_0x203b6d["get"](_0x2ee5d7);
    if (_0x34ec3a) {
      _0x34ec3a["refCount"] -= 0x1;
      if (_0x34ec3a["refCount"] <= 0x0) {
        try {
          await _0x34ec3a["server"]["close"](true);
        } catch (_0x224a88) {
          console["warn"]("Error closing proxy server:", _0x224a88);
        }
        a0_0x203b6d["delete"](_0x2ee5d7);
      }
    }
  }
  const a0_0x2d67fc = createRequire(import.meta["url"]),
    a0_0x3d9dbf = a0_0x25b7e1["dirname"](fileURLToPath(import.meta["url"])),
    a0_0x1877ee = new a0_0x4f3194(),
    { machineIdSync: a0_0x55cdf3 } = a0_0x4c0123;
  let a0_0xbd0a27 = null;
  var a0_0x22683f = [
    "愿家人喜乐盈满人生，春暖花开心情好，快乐长伴如星辰。",
    "愿你和家人好运常伴左右，春暖花开心情好，平安护佑每刻。",
    "祝福家人喜乐盈满人生，每天皆有好心情，福气连连好运来。",
    "愿亲人们所求皆如愿，每天皆有好心情，春赏花开夏听雨。",
    "愿家人岁岁平安顺意，笑容灿烂如春花，笑口常开心花放。",
    "愿亲人们所求皆如愿，春暖花开心情好，笑口常开心花放。",
    "祝家人岁岁平安顺意，欢声笑语满屋绕，甜蜜如蜜糖。",
    "愿家人所求皆如愿，前程似锦万事兴，好梦连连到天明。",
    "愿家人好运常伴左右，福星高照绕家门，心情舒畅无烦忧。",
    "愿亲人们烦恼随风去，生活如意似春风，红火又顺心。",
    "愿家人岁岁平安顺意，欢声笑语满屋绕，福气连连好运来。",
    "祝愿家人三餐四季皆温暖，欢声笑语满屋绕，好梦连连到天明。",
    "愿亲人们三餐四季皆温暖，生活如意似春风，笑口常开心花放。",
    "祝家人三餐四季皆温暖，福星高照绕家门，心情舒畅无烦忧。",
    "愿你和家人三餐四季皆温暖，爱意浓浓绕心头，春赏花开夏听雨。",
    "祝福家人家庭温馨和美，生活如意似春风，快乐长伴如星辰。",
    "祝愿家人喜乐盈满人生，笑容灿烂如春花，红火又顺心。",
    "祝家人健康常伴左右，爱意浓浓绕心头，笑口常开心花放。",
    "愿你和家人日子越过越甜，每天皆有好心情，心情舒畅无烦忧。",
    "祝家人所求皆如愿，欢声笑语满屋绕，甜蜜如蜜糖。",
    "祝家人家庭温馨和美，前程似锦万事兴，笑口常开心花放。",
    "祝愿家人健康常伴左右，前程似锦万事兴，甜蜜如蜜糖。",
    "愿你和家人身心康健福满堂，笑容灿烂如春花，好梦连连到天明。",
    "祝福家人健康常伴左右，福星高照绕家门，快乐长伴如星辰。",
    "愿家人岁岁平安顺意，欢声笑语满屋绕，春赏花开夏听雨。",
    "祝福家人好运常伴左右，前程似锦万事兴，心情舒畅无烦忧。",
    "祝福家人所求皆如愿，生活如意似春风，心情舒畅无烦忧。",
    "愿你和家人家庭温馨和美，欢声笑语满屋绕，春赏花开夏听雨。",
    "祝福家人所求皆如愿，笑容灿烂如春花，平安护佑每刻。",
    "祝愿家人好运常伴左右，福星高照绕家门，红火又顺心。",
    "愿亲人们健康常伴左右，春暖花开心情好，四季皆是好时光。",
    "祝家人三餐四季皆温暖，笑容灿烂如春花，快乐长伴如星辰。",
    "愿你和家人好运常伴左右，前程似锦万事兴，平安护佑每刻。",
    "愿你和家人岁岁平安顺意，幸福洋溢像阳光，春赏花开夏听雨。",
    "祝愿家人好运常伴左右，欢声笑语满屋绕，红火又顺心。",
    "祝福家人喜乐盈满人生，生活如意似春风，红火又顺心。",
    "祝愿家人三餐四季皆温暖，生活如意似春风，好梦连连到天明。",
    "愿亲人们所求皆如愿，生活如意似春风，平安护佑每刻。",
    "愿你和家人岁岁平安顺意，生活如意似春风，红火又顺心。",
    "愿家人三餐四季皆温暖，幸福洋溢像阳光，快乐长伴如星辰。",
    "祝家人所求皆如愿，前程似锦万事兴，福气连连好运来。",
    "愿亲人们好运常伴左右，天伦之乐永不断，心情舒畅无烦忧。",
    "愿你和家人三餐四季皆温暖，爱意浓浓绕心头，笑口常开心花放。",
    "愿亲人们喜乐盈满人生，生活如意似春风，笑口常开心花放。",
    "祝愿家人烦恼随风去，前程似锦万事兴，红火又顺心。",
    "愿你和家人身心康健福满堂，前程似锦万事兴，好梦连连到天明。",
    "愿亲人们所求皆如愿，幸福洋溢像阳光，快乐长伴如星辰。",
    "愿亲人们烦恼随风去，天伦之乐永不断，平安护佑每刻。",
    "祝福家人身心康健福满堂，生活如意似春风，四季皆是好时光。",
    "愿家人喜乐盈满人生，春暖花开心情好，四季皆是好时光。",
    "愿亲人们喜乐盈满人生，幸福洋溢像阳光，心情舒畅无烦忧。",
    "愿亲人们日子越过越甜，爱意浓浓绕心头，心情舒畅无烦忧。",
    "愿你和家人岁岁平安顺意，前程似锦万事兴，心情舒畅无烦忧。",
    "愿家人所求皆如愿，福星高照绕家门，福气连连好运来。",
    "愿你和家人健康常伴左右，爱意浓浓绕心头，平安护佑每刻。",
    "愿家人好运常伴左右，春暖花开心情好，四季皆是好时光。",
    "祝愿家人烦恼随风去，欢声笑语满屋绕，红火又顺心。",
    "愿亲人们好运常伴左右，福星高照绕家门，快乐长伴如星辰。",
    "祝愿家人岁岁平安顺意，笑容灿烂如春花，好梦连连到天明。",
    "祝福家人日子越过越甜，春暖花开心情好，福气连连好运来。",
    "愿家人所求皆如愿，爱意浓浓绕心头，快乐长伴如星辰。",
    "祝愿家人身心康健福满堂，每天皆有好心情，福气连连好运来。",
    "祝愿家人烦恼随风去，福星高照绕家门，快乐长伴如星辰。",
    "愿亲人们岁岁平安顺意，爱意浓浓绕心头，四季皆是好时光。",
    "祝家人三餐四季皆温暖，幸福洋溢像阳光，好梦连连到天明。",
    "祝福家人三餐四季皆温暖，生活如意似春风，心情舒畅无烦忧。",
    "祝愿家人岁岁平安顺意，春暖花开心情好，快乐长伴如星辰。",
    "祝愿家人好运常伴左右，生活如意似春风，福气连连好运来。",
    "愿家人岁岁平安顺意，春暖花开心情好，心情舒畅无烦忧。",
    "祝福家人健康常伴左右，天伦之乐永不断，甜蜜如蜜糖。",
    "祝福家人喜乐盈满人生，春暖花开心情好，心情舒畅无烦忧。",
    "愿亲人们身心康健福满堂，春暖花开心情好，甜蜜如蜜糖。",
    "祝福家人日子越过越甜，爱意浓浓绕心头，好梦连连到天明。",
    "愿家人好运常伴左右，前程似锦万事兴，好梦连连到天明。",
    "愿你和家人健康常伴左右，每天皆有好心情，红火又顺心。",
    "愿亲人们日子越过越甜，笑容灿烂如春花，甜蜜如蜜糖。",
    "愿家人健康常伴左右，笑容灿烂如春花，福气连连好运来。",
    "祝愿家人家庭温馨和美，爱意浓浓绕心头，笑口常开心花放。",
    "祝愿家人好运常伴左右，前程似锦万事兴，春赏花开夏听雨。",
    "祝家人家庭温馨和美，生活如意似春风，甜蜜如蜜糖。",
    "愿家人好运常伴左右，前程似锦万事兴，春赏花开夏听雨。",
    "祝家人健康常伴左右，春暖花开心情好，春赏花开夏听雨。",
    "愿你和家人身心康健福满堂，爱意浓浓绕心头，好梦连连到天明。",
    "祝福家人好运常伴左右，前程似锦万事兴，红火又顺心。",
    "愿家人岁岁平安顺意，天伦之乐永不断，春赏花开夏听雨。",
    "愿亲人们所求皆如愿，生活如意似春风，笑口常开心花放。",
    "祝愿家人日子越过越甜，春暖花开心情好，平安护佑每刻。",
    "愿家人家庭温馨和美，生活如意似春风，甜蜜如蜜糖。",
    "愿你和家人健康常伴左右，前程似锦万事兴，好梦连连到天明。",
    "祝福家人三餐四季皆温暖，欢声笑语满屋绕，四季皆是好时光。",
    "愿亲人们好运常伴左右，爱意浓浓绕心头，甜蜜如蜜糖。",
    "愿家人日子越过越甜，福星高照绕家门，甜蜜如蜜糖。",
    "愿你和家人身心康健福满堂，天伦之乐永不断，四季皆是好时光。",
    "祝愿家人喜乐盈满人生，生活如意似春风，红火又顺心。",
    "祝家人好运常伴左右，爱意浓浓绕心头，快乐长伴如星辰。",
    "祝愿家人烦恼随风去，幸福洋溢像阳光，甜蜜如蜜糖。",
    "愿你和家人家庭温馨和美，前程似锦万事兴，好梦连连到天明。",
    "祝福家人所求皆如愿，福星高照绕家门，心情舒畅无烦忧。",
    "祝愿家人日子越过越甜，生活如意似春风，心情舒畅无烦忧。",
    "愿你和家人喜乐盈满人生，笑容灿烂如春花，春赏花开夏听雨。",
    "愿你和家人家庭温馨和美，春暖花开心情好，快乐长伴如星辰。",
    "祝家人身心康健福满堂，欢声笑语满屋绕，笑口常开心花放。",
    "祝福家人岁岁平安顺意，笑容灿烂如春花，春赏花开夏听雨。",
    "祝福家人岁岁平安顺意，天伦之乐永不断，春赏花开夏听雨。",
    "祝家人三餐四季皆温暖，幸福洋溢像阳光，笑口常开心花放。",
    "愿亲人们三餐四季皆温暖，爱意浓浓绕心头，好梦连连到天明。",
    "祝愿家人家庭温馨和美，爱意浓浓绕心头，四季皆是好时光。",
    "愿亲人们日子越过越甜，欢声笑语满屋绕，好梦连连到天明。",
    "愿家人喜乐盈满人生，爱意浓浓绕心头，甜蜜如蜜糖。",
    "祝福家人好运常伴左右，爱意浓浓绕心头，福气连连好运来。",
    "愿你和家人好运常伴左右，前程似锦万事兴，四季皆是好时光。",
    "祝家人健康常伴左右，欢声笑语满屋绕，春赏花开夏听雨。",
    "愿家人所求皆如愿，爱意浓浓绕心头，笑口常开心花放。",
    "愿亲人们喜乐盈满人生，春暖花开心情好，平安护佑每刻。",
    "祝福家人身心康健福满堂，欢声笑语满屋绕，平安护佑每刻。",
    "愿亲人们烦恼随风去，天伦之乐永不断，甜蜜如蜜糖。",
    "愿你和家人身心康健福满堂，生活如意似春风，心情舒畅无烦忧。",
    "愿亲人们三餐四季皆温暖，幸福洋溢像阳光，笑口常开心花放。",
    "祝家人所求皆如愿，爱意浓浓绕心头，笑口常开心花放。",
    "祝家人所求皆如愿，幸福洋溢像阳光，春赏花开夏听雨。",
    "祝家人喜乐盈满人生，天伦之乐永不断，红火又顺心。",
    "愿家人喜乐盈满人生，幸福洋溢像阳光，甜蜜如蜜糖。",
    "祝福家人岁岁平安顺意，生活如意似春风，福气连连好运来。",
    "祝愿家人日子越过越甜，爱意浓浓绕心头，快乐长伴如星辰。",
    "愿你和家人家庭温馨和美，欢声笑语满屋绕，笑口常开心花放。",
    "祝愿家人健康常伴左右，爱意浓浓绕心头，四季皆是好时光。",
    "愿亲人们身心康健福满堂，爱意浓浓绕心头，快乐长伴如星辰。",
    "祝家人所求皆如愿，天伦之乐永不断，快乐长伴如星辰。",
    "愿家人岁岁平安顺意，春暖花开心情好，甜蜜如蜜糖。",
    "愿你和家人家庭温馨和美，生活如意似春风，红火又顺心。",
    "祝福家人好运常伴左右，天伦之乐永不断，红火又顺心。",
    "愿亲人们日子越过越甜，幸福洋溢像阳光，红火又顺心。",
    "愿家人喜乐盈满人生，每天皆有好心情，心情舒畅无烦忧。",
    "愿亲人们喜乐盈满人生，笑容灿烂如春花，心情舒畅无烦忧。",
    "愿你和家人身心康健福满堂，笑容灿烂如春花，春赏花开夏听雨。",
    "愿家人所求皆如愿，爱意浓浓绕心头，四季皆是好时光。",
    "祝家人岁岁平安顺意，爱意浓浓绕心头，快乐长伴如星辰。",
    "祝家人好运常伴左右，福星高照绕家门，快乐长伴如星辰。",
    "愿你和家人家庭温馨和美，天伦之乐永不断，春赏花开夏听雨。",
    "祝愿家人所求皆如愿，爱意浓浓绕心头，好梦连连到天明。",
    "愿亲人们三餐四季皆温暖，欢声笑语满屋绕，四季皆是好时光。",
    "祝愿家人三餐四季皆温暖，前程似锦万事兴，红火又顺心。",
    "愿你和家人家庭温馨和美，春暖花开心情好，笑口常开心花放。",
    "愿你和家人所求皆如愿，生活如意似春风，红火又顺心。",
    "愿你和家人烦恼随风去，生活如意似春风，快乐长伴如星辰。",
    "祝家人好运常伴左右，每天皆有好心情，红火又顺心。",
    "愿你和家人日子越过越甜，爱意浓浓绕心头，好梦连连到天明。",
    "祝家人身心康健福满堂，爱意浓浓绕心头，平安护佑每刻。",
    "祝愿家人日子越过越甜，笑容灿烂如春花，春赏花开夏听雨。",
    "祝家人家庭温馨和美，每天皆有好心情，心情舒畅无烦忧。",
    "祝愿家人喜乐盈满人生，幸福洋溢像阳光，快乐长伴如星辰。",
    "愿你和家人身心康健福满堂，前程似锦万事兴，快乐长伴如星辰。",
    "愿家人好运常伴左右，生活如意似春风，甜蜜如蜜糖。",
    "祝福家人好运常伴左右，前程似锦万事兴，甜蜜如蜜糖。",
    "祝愿家人烦恼随风去，生活如意似春风，福气连连好运来。",
    "愿你和家人三餐四季皆温暖，幸福洋溢像阳光，甜蜜如蜜糖。",
    "愿你和家人所求皆如愿，欢声笑语满屋绕，春赏花开夏听雨。",
    "祝福家人岁岁平安顺意，生活如意似春风，平安护佑每刻。",
    "祝福家人家庭温馨和美，福星高照绕家门，好梦连连到天明。",
    "愿你和家人喜乐盈满人生，每天皆有好心情，甜蜜如蜜糖。",
    "祝福家人所求皆如愿，福星高照绕家门，福气连连好运来。",
    "愿亲人们岁岁平安顺意，欢声笑语满屋绕，四季皆是好时光。",
    "祝福家人健康常伴左右，生活如意似春风，平安护佑每刻。",
    "祝家人岁岁平安顺意，笑容灿烂如春花，福气连连好运来。",
    "祝福家人三餐四季皆温暖，生活如意似春风，红火又顺心。",
    "愿你和家人岁岁平安顺意，爱意浓浓绕心头，好梦连连到天明。",
    "愿家人所求皆如愿，欢声笑语满屋绕，福气连连好运来。",
    "愿亲人们好运常伴左右，前程似锦万事兴，好梦连连到天明。",
    "祝家人健康常伴左右，福星高照绕家门，笑口常开心花放。",
    "祝福家人家庭温馨和美，爱意浓浓绕心头，好梦连连到天明。",
    "祝愿家人烦恼随风去，春暖花开心情好，平安护佑每刻。",
    "祝家人家庭温馨和美，爱意浓浓绕心头，笑口常开心花放。",
    "祝愿家人岁岁平安顺意，福星高照绕家门，笑口常开心花放。",
    "祝福家人所求皆如愿，笑容灿烂如春花，甜蜜如蜜糖。",
    "祝家人所求皆如愿，天伦之乐永不断，平安护佑每刻。",
    "愿亲人们家庭温馨和美，天伦之乐永不断，好梦连连到天明。",
    "祝福家人岁岁平安顺意，欢声笑语满屋绕，快乐长伴如星辰。",
    "愿家人日子越过越甜，欢声笑语满屋绕，心情舒畅无烦忧。",
    "愿你和家人家庭温馨和美，爱意浓浓绕心头，春赏花开夏听雨。",
    "祝福家人家庭温馨和美，春暖花开心情好，笑口常开心花放。",
    "愿家人所求皆如愿，每天皆有好心情，平安护佑每刻。",
    "祝愿家人好运常伴左右，春暖花开心情好，四季皆是好时光。",
    "愿亲人们烦恼随风去，幸福洋溢像阳光，福气连连好运来。",
    "祝福家人健康常伴左右，福星高照绕家门，甜蜜如蜜糖。",
    "祝家人家庭温馨和美，生活如意似春风，快乐长伴如星辰。",
    "愿家人烦恼随风去，欢声笑语满屋绕，春赏花开夏听雨。",
    "愿亲人们喜乐盈满人生，生活如意似春风，平安护佑每刻。",
    "祝福家人家庭温馨和美，欢声笑语满屋绕，春赏花开夏听雨。",
    "愿你和家人日子越过越甜，幸福洋溢像阳光，福气连连好运来。",
    "祝福家人喜乐盈满人生，天伦之乐永不断，平安护佑每刻。",
    "愿亲人们日子越过越甜，春暖花开心情好，笑口常开心花放。",
    "祝福家人日子越过越甜，笑容灿烂如春花，快乐长伴如星辰。",
    "祝家人三餐四季皆温暖，春暖花开心情好，平安护佑每刻。",
    "愿你和家人身心康健福满堂，笑容灿烂如春花，笑口常开心花放。",
    "祝家人所求皆如愿，欢声笑语满屋绕，福气连连好运来。",
    "愿亲人们所求皆如愿，生活如意似春风，福气连连好运来。",
    "祝家人三餐四季皆温暖，幸福洋溢像阳光，春赏花开夏听雨。",
    "愿亲人们所求皆如愿，前程似锦万事兴，甜蜜如蜜糖。",
    "祝愿家人家庭温馨和美，前程似锦万事兴，笑口常开心花放。",
    "你这条视频我竟然看了三遍",
    "画面挺简单但意外有感觉",
    "感觉你平时是个特别细心的人",
    "这种节奏特别舒服，挺耐看",
    "不太会拍但挺有味道的",
    "你这条我忍不住点赞了",
    "看着挺稳的人，气质也好",
    "视频有点意思，很想看下一条",
    "说不上哪里好，反正挺吸引人",
    "你笑起来挺有感染力的",
    "镜头里透着一种特别的真实",
    "你这样发很自然，让人喜欢",
    "动作不多，但挺让人想看完",
    "不刻意的样子最打动人",
    "你这风格我还真挺喜欢",
    "普通场景但拍出了感觉",
    "视频挺随意的，我却很喜欢",
    "你讲话方式我还挺想听的",
    "看你做事特别安心的感觉",
    "气质这种东西，藏不住的",
    "每个动作都看得出生活气",
    "我居然被你的视频治愈了",
    "刷到你心情忽然变好了",
    "拍得挺简单但挺走心的",
    "这种静静拍的风格真不错",
    "视频好像没剪辑，但挺耐看",
    "像你这样的人挺少见了",
    "你这条评论区好干净舒服",
    "从你视频里看得出稳重",
    "背景乐选得太对味了",
    "拍得挺轻松，氛围感拉满",
    "看得出你是个靠谱的人",
    "想知道你平时都拍些什么",
    "你生活的样子有点吸引人",
    "视频不长但我却记住了你",
    "拍得不复杂但挺走心的",
    "你这种人设反而最讨喜",
    "画面安静但不沉闷，刚刚好",
    "不经意一刷，竟然停下来了",
    "你这条视频挺有分寸感的",
    "气场特别稳，给人安全感",
    "没想到会因为你驻足几秒",
    "我就静静看完，也没跳过",
    "你镜头后那种状态挺迷人",
    "这条我收藏了，准备回看",
    "生活类的拍成这样不容易",
    "你做事的样子有点上头",
    "不夸张不造作，这样最好",
    "我喜欢你这种安静分享方式",
    "这种感觉，像认识的人一样",
    "越刷越觉得你挺特别的",
  ];
  const a0_0x1340d3 =
    "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyJeZ5txF8dTpDT/C9pN2\ns9KdV/nbQGhJ62ANjxg6w0v2mAyqlocdYz88N0iE5ZBhJeELqmgJjK7PQKwkv0Gv\nD1bNmka7GjHIieNvOiyvLd5i727UTnpv51txZmBqF4YBdFTAdP0ASVVIotMOJhOM\n8vt7BislfwONtrrtJR/TSprw85gYCITyE50BZZalQfwKCFiPvZ8RBQ8VNuYJlnym\njdQRjJ5eXKBUHBFqXbJZIYYTlFsvAS3Or7xM9qRoNXVsAsJk9eS406itV0CU8Kpv\nRbu3i9cr50ILJNJWE639960QS+hQrx2xRbDBoegqyDgQSaNdyVpOvPD54e9jakT0\nOwIDAQAB\n-----END PUBLIC KEY-----\n";
  process["env"]["APP_ROOT"] = a0_0x25b7e1["join"](a0_0x3d9dbf, "../..");
  const a0_0x2769b8 = a0_0x25b7e1["join"](
      process["env"]["APP_ROOT"],
      "dist-electron",
    ),
    a0_0x3411c3 = a0_0x25b7e1["join"](process["env"]["APP_ROOT"], "dist"),
    a0_0x4427ed = process["env"]["VITE_DEV_SERVER_URL"];
  process["env"]["VITE_PUBLIC"] = a0_0x4427ed
    ? a0_0x25b7e1["join"](process["env"]["APP_ROOT"], "public")
    : a0_0x3411c3;
  let a0_0x16772c = null;
  checkForUpdate();
  const a0_0x4cdbf2 = a0_0x25b7e1["join"](a0_0x3d9dbf, "../preload/index.js"),
    a0_0x22c15f = a0_0x25b7e1["join"](a0_0x3411c3, "index.html"),
    a0_0x48fbde = app["isPackaged"]
      ? a0_0x25b7e1["join"](a0_0x3411c3, "favicon.ico")
      : a0_0x25b7e1["join"](a0_0x3d9dbf, "../../favicon.ico");
  ipcMain["handle"]("get-device-id", async () => {
    try {
      return a0_0x55cdf3(true);
    } catch (_0x434a61) {
      return "";
    }
  });
  function a0_0x1680c7() {
    (a0_0x16772c = new BrowserWindow({
      width: 0x4b0,
      height: 0x320,
      icon: a0_0x48fbde,
      webPreferences: {
        devTools: false,
        preload: a0_0x4cdbf2,
        additionalArguments: ["--myflag=superwindow"],
      },
      title: "超凡 3.0.0",
    })),
      a0_0x16772c["webContents"]["on"]("devtools-opened", () =>
        a0_0x16772c["close"](),
      ),
      a0_0x4427ed
        ? a0_0x16772c["loadURL"](a0_0x4427ed)
        : a0_0x16772c["loadFile"](a0_0x22c15f),
      Menu["setApplicationMenu"](Menu["buildFromTemplate"]([])),
      a0_0x16772c["webContents"]["on"]("did-finish-load", () => {
        a0_0x16772c == null
          ? undefined
          : a0_0x16772c["webContents"]["send"](
              "main-process-message",
              new Date()["toLocaleString"](),
            );
      }),
      a0_0x16772c["webContents"]["setWindowOpenHandler"](({ url: _0x210c0a }) => {
        if (/^https?:/["test"](_0x210c0a))
          return (
            shell["openExternal"](_0x210c0a),
            {
              action: "deny",
            }
          );
        return {
          action: "deny",
        };
      });
  }
  app["whenReady"]()["then"](a0_0x1680c7),
    app["on"]("window-all-closed", () => {
      a0_0x16772c = null;
      if (process["platform"] !== "darwin") app["quit"]();
      if (
        a0_0xbd0a27 &&
        typeof a0_0xbd0a27["exports"]["selfDestruct"] === "function"
      ) {
        a0_0xbd0a27["exports"]["selfDestruct"]();
      }
    }),
    app["on"]("before-quit", () => {
      if (a0_0x39e2da) clearInterval(a0_0x39e2da);
      a0_0xbd0a27 &&
        typeof a0_0xbd0a27["exports"]["selfDestruct"] === "function" &&
        a0_0xbd0a27["exports"]["selfDestruct"]();
    }),
    app["on"]("activate", () => {
      if (BrowserWindow["getAllWindows"]()["length"])
        BrowserWindow["getAllWindows"]()[0x0]["focus"]();
      else a0_0x1680c7();
    }),
    ipcMain["handle"]("get-store", (_0x1c820e, _0x29eb0a) => {
      return a0_0x1877ee["get"](_0x29eb0a);
    }),
    ipcMain["handle"]("set-store", (_0x291153, _0x496efd, _0x139e28) => {
      return a0_0x1877ee["set"](_0x496efd, _0x139e28), true;
    });
  const a0_0x2e661f = {
    app: app,
    BrowserWindow: BrowserWindow,
    shell: shell,
    Menu: Menu,
    ipcMain: ipcMain,
    session: session,
    dialog: dialog,
    screen: screen,
    net: a0_0x591156,
    clipboard: clipboard,
    webContents: webContents,
    protocol: protocol,
    path: a0_0x25b7e1,
    os: a0_0x5610a7,
    fs: a0_0x53c070,
    store: a0_0x1877ee,
    win: a0_0x16772c,
    preload: a0_0x4cdbf2,
    indexHtml: a0_0x22c15f,
    iconPath: a0_0x48fbde,
    publicKey: a0_0x1340d3,
    require: a0_0x2d67fc,
    __dirname: a0_0x3d9dbf,
    zhufuyu: a0_0x22683f,
    UserAgent: a0_0x2dbb00,
    keyboard: keyboard,
    Key: Key,
    pauseAutomation: a0_0x54f2ca,
    resumeAutomation: a0_0x5e022e,
    waitIfPaused: a0_0x8c077b,
    initWindowPauseState: a0_0x3c3339,
    destroyWindowPauseState: a0_0x291dbc,
    setWinStep: a0_0x5e8148,
    getWinStep: a0_0x3a7dd4,
    delWinStep: a0_0x52d860,
    getOrCreateProxy: a0_0x17a25f,
    releaseProxy: a0_0x3cac28,
    machineIdSync: a0_0x55cdf3,
    setTimeout: setTimeout,
    clearInterval: clearInterval,
    clearTimeout: clearTimeout,
    Buffer: Buffer,
    console: console,
    process: process,
    fetch: a0_0x4a0e80,
    global: global,
    crypto: a0_0x4939ae,
    CryptoJS: a0_0x373525,
  };
  function a0_0xd5c6a7() {
    if (
      process["execArgv"]["some"](
        (_0x5b5b01) =>
          _0x5b5b01["includes"]("inspect") || _0x5b5b01["includes"]("debug"),
      )
    )
      process["exit"](0x1);
    if (
      process["env"]["NODE_OPTIONS"] &&
      process["env"]["NODE_OPTIONS"]["includes"]("inspect")
    )
      process["exit"](0x1);
    if (
      process["env"]["ELECTRON_RUN_AS_NODE"] ||
      process["env"]["ELECTRON_ENABLE_LOGGING"]
    )
      process["exit"](0x1);
  }
  let a0_0x39e2da = setInterval(a0_0xd5c6a7, 0xbb8);
  async function a0_0x124f68(_0x3e7596, _0xe17368, _0x53c59c = {}) {
    const _0x49c0ff = await a0_0x4a0e80(_0x3e7596, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ..._0x53c59c,
      },
      body: _0xe17368,
    });
    console.log(_0x49c0ff);
    return await _0x49c0ff.text();
  }

  async function a0_0x124f68json(_0x3e7596, _0xe17368, _0x53c59c = {}) {
    const _0x49c0ff = await a0_0x4a0e80(_0x3e7596, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ..._0x53c59c,
      },
      body: _0xe17368,
    });
    return await _0x49c0ff.json();
  }

  async function a0_0x5d5953(_0x14512b) {
    const _0x56774c = Date["now"]() + ":破解死一户口本",
      _0x12ba69 = Buffer["from"](_0x56774c, "utf8"),
      _0x325808 = a0_0x4939ae["publicEncrypt"](a0_0x1340d3, _0x12ba69);
    console["log"](_0x325808["toString"]("base64"));
    const _0x24e845 = await a0_0x124f68(
        "http://160.202.228.242:85/pc/mainCode.php",
        {},
      );
    console.log("解密后的脚本内容:", _0x24e845);
    const _0x13e856 = _0x24e845,
      _0x20213c = {
        ..._0x14512b,
        exports: {},
        setInterval: setInterval,
        setTimeout: setTimeout,
        clearInterval: clearInterval,
        clearTimeout: clearTimeout,
        Buffer: Buffer,
        console: console,
        process: process,
        fetch: a0_0x4a0e80,
        global: global,
        crypto: a0_0x4939ae,
        CryptoJS: a0_0x373525,
      },
      _0x59c89f = new a0_0x5f3bf9["Script"](_0x13e856, {
        filename: "business-main.js",
      }),
      _0x558ed2 = a0_0x5f3bf9["createContext"](_0x20213c);
    _0x59c89f["runInContext"](_0x558ed2), (a0_0xbd0a27 = _0x20213c);
    if (typeof _0x20213c["exports"]["injectBusinessLogic"] === "function") {
      _0x20213c["exports"]["injectBusinessLogic"](_0x14512b);
    } else {
      throw new Error("business-main.js未正确导出injectBusinessLogic");
    }
}
  app["disableHardwareAcceleration"](),
    app["whenReady"]()["then"](() => {
      a0_0xd5c6a7(), a0_0x5d5953(a0_0x2e661f)["catch"](console["error"]);
    });
  export {
    a0_0x2769b8 as MAIN_DIST,
    a0_0x3411c3 as RENDERER_DIST,
    a0_0x4427ed as VITE_DEV_SERVER_URL,
  };
  