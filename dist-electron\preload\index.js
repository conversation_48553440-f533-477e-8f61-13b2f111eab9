"use strict";

const a0_0x551e94 = require("electron");
if (
  !(() => {
    try {
      return (
        !(
          "undefined" == typeof process || !Array["isArray"](process["argv"])
        ) &&
        process["argv"]["some"](
          (_0x13d1e7) => "--myflag=superwindow" === _0x13d1e7,
        )
      );
    } catch {
      return false;
    }
  })()
)
  throw new Error("err!");
let a0_0x425725;
const a0_0x432ec1 = [],
  a0_0x564a57 = [],
  a0_0x3e8195 = [];
function a0_0x17eda6(_0x21c631 = ["complete", "interactive"]) {
  return new Promise((_0x318347) => {
    _0x21c631["includes"](document["readyState"])
      ? _0x318347(true)
      : document["addEventListener"]("readystatechange", () => {
          _0x21c631["includes"](document["readyState"]) && _0x318347(true);
        });
  });
}
a0_0x551e94["ipcRenderer"]["on"]("set-win-id", (_0xf5997e, _0x35d7ae) => {
  try {
    (a0_0x425725 = _0x35d7ae), sessionStorage["setItem"]("WIN_ID", _0x35d7ae);
  } catch (_0x281f6f) {}
}),
  a0_0x551e94["contextBridge"]["exposeInMainWorld"]("ipcRenderer", {
    on(..._0x535034) {
      const [_0x3e1a98, _0x2dfe64] = _0x535034;
      return a0_0x551e94["ipcRenderer"]["on"](
        _0x3e1a98,
        (_0x5b11a9, ..._0xa7e25f) => _0x2dfe64(_0x5b11a9, ..._0xa7e25f),
      );
    },
    off(..._0x2f9cac) {
      const [_0x26c0af, ..._0xc7d002] = _0x2f9cac;
      return a0_0x551e94["ipcRenderer"]["off"](_0x26c0af, ..._0xc7d002);
    },
    send(..._0x1f8c8e) {
      const [_0x41e098, ..._0x5ed504] = _0x1f8c8e;
      return a0_0x551e94["ipcRenderer"]["send"](_0x41e098, ..._0x5ed504);
    },
    invoke(..._0x5069c8) {
      const [_0x3387e4, ..._0x3ec590] = _0x5069c8;
      return a0_0x551e94["ipcRenderer"]["invoke"](_0x3387e4, ..._0x3ec590);
    },
  });
const a0_0x4ea7f6 = {
  append(_0x16600d, _0x5e80b4) {
    if (
      !Array["from"](_0x16600d["children"])["find"](
        (_0x506a2d) => _0x506a2d === _0x5e80b4,
      )
    )
      return _0x16600d["appendChild"](_0x5e80b4);
  },
  remove(_0x269b2b, _0x3a6cd7) {
    if (
      Array["from"](_0x269b2b["children"])["find"](
        (_0x337df1) => _0x337df1 === _0x3a6cd7,
      )
    )
      return _0x269b2b["removeChild"](_0x3a6cd7);
  },
};
function a0_0x23ed35(_0x42f019, _0x5922b9) {
  const _0x1bb8c2 = setInterval(_0x42f019, _0x5922b9);
  return a0_0x432ec1["push"](_0x1bb8c2), _0x1bb8c2;
}
function a0_0x581314(_0x4cd7bd, _0x571ce0, _0x421f77, _0x40d5b3) {
  _0x4cd7bd["addEventListener"](_0x571ce0, _0x421f77, _0x40d5b3),
    a0_0x3e8195["push"]({
      target: _0x4cd7bd,
      event: _0x571ce0,
      handler: _0x421f77,
      opts: _0x40d5b3,
    });
}
const a0_0x2a2a71 = (_0x33e553, _0x4ad207) =>
  new Promise((_0xb0b38e) => {
    const _0xb7b74c =
      Math["floor"](Math["random"]() * (_0x4ad207 - _0x33e553 + 0x1)) +
      _0x33e553;
    setTimeout(_0xb0b38e, _0xb7b74c);
  });
function a0_0x3c2a88(_0xdaae6e, _0x2447e9) {
  return (
    Math["floor"](Math["random"]() * (_0x2447e9 - _0xdaae6e + 0x1)) + _0xdaae6e
  );
}
function a0_0x57b9b3(_0x57703c) {
  let _0x23d677 = document["createElement"]("div");
  (_0x23d677["innerText"] = _0x57703c),
    Object["assign"](_0x23d677["style"], {
      position: "fixed",
      top: "20%",
      left: "50%",
      transform: "translate(-50%, -50%)",
      background: "rgba(0,0,0,0.85)",
      color: "#fff",
      padding: "16px 32px",
      borderRadius: "8px",
      fontSize: "20px",
      zIndex: 0x1869f,
      boxShadow: "0 2px 16px rgba(0,0,0,0.2)",
      textAlign: "center",
      pointerEvents: "none",
    }),
    document["body"]["appendChild"](_0x23d677),
    setTimeout(() => {
      _0x23d677["remove"]();
    }, 0x898);
}
a0_0x551e94["contextBridge"]["exposeInMainWorld"]("electron", {
  clearAllAccountData: async () => {
    try {
      return (
        document["cookie"]["split"](";")["forEach"](function (_0x495679) {
          document["cookie"] = _0x495679["replace"](/^ +/, "")["replace"](
            /=.*/,
            "=;expires=" + new Date()["toUTCString"]() + ";path=/",
          );
        }),
        localStorage["clear"](),
        sessionStorage["clear"](),
        true
      );
    } catch (_0x104d89) {}
  },
  checkLoginStatus: async (_0x220033) => {
    try {
      var _0x5324e1 = (_0x220033 =
        "string" == typeof _0x220033 ? JSON["parse"](_0x220033) : _0x220033)[
        "id"
      ];
      (a0_0x425725 = _0x220033["rowId"]),
        sessionStorage["setItem"]("WIN_ID", a0_0x425725);
      var _0x2eefed = _0x220033["rowId"];
      let _0x371e7c = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x2eefed,
        },
      );
      var _0x137887 = null == _0x371e7c ? undefined : _0x371e7c["options"];
      if (_0x137887) {
        (_0x137887["status"] = "检查登陆状态"),
          (_0x137887["lastOprateTime"] = Date["now"]()),
          await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
            action: "set",
            id: _0x2eefed,
            newRow: {
              options: _0x137887,
            },
          }),
          await a0_0x2b2e45(_0x2eefed);
        const _0x2f4cf2 = await a0_0x1b7b17();
        (_0x137887["guanzhuNum"] = _0x2f4cf2["followCount"]),
          (_0x137887["fensiNum"] = _0x2f4cf2["fansCount"]),
          (_0x137887["userName"] = _0x2f4cf2["username"]),
          (_0x137887["status"] = "已登陆账号"),
          (_0x137887["lastOprateTime"] = Date["now"]()),
          await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
            action: "set",
            id: _0x2eefed,
            newRow: {
              options: _0x137887,
            },
          });
      }
      return (
        "home" == _0x5324e1
          ? a0_0x3ea95c("https://www.douyin.com/?recommend=1&from_nav=1")
          : await a0_0x25e2c8(_0x5324e1),
        true
      );
    } catch (_0x2463f2) {}
  },
  goSearchPage: async (_0x1f0c02) => {
    try {
      var _0x47d80f = sessionStorage["getItem"]("WIN_ID");
      let _0xcbe644 = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x47d80f,
        },
      );
      var _0x1cfbb3 = null == _0xcbe644 ? undefined : _0xcbe644["options"];
      (_0x1cfbb3["status"] = "搜索抖音号"),
        (_0x1cfbb3["lastOprateTime"] = Date["now"]()),
        await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
          action: "set",
          id: _0x47d80f,
          newRow: {
            options: _0x1cfbb3,
          },
        });
      if (await a0_0x295ba3("span", _0x1f0c02)) {
        await a0_0x2a2a71(0x4b0, 0x708);
        const _0x1b4792 = await a0_0x1dcfdf(_0x1f0c02);
        return (
          await a0_0x2a2a71(0x4b0, 0x708),
          await a0_0x551e94["ipcRenderer"]["invoke"]("searchUrl-check", {
            url: _0x1b4792,
          }),
          !!_0x1b4792 && (await a0_0x3ea95c(_0x1b4792), true)
        );
      }
      return false;
    } catch (_0x35f772) {}
  },
  goIdPage: async (_0x59db49) => {
    try {
      var _0x444868 = sessionStorage["getItem"]("WIN_ID");
      let _0x29e9b8 = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x444868,
        },
      );
      var _0x40114f = null == _0x29e9b8 ? undefined : _0x29e9b8["options"];
      (_0x40114f["status"] = "获取粉丝中..."),
        (_0x40114f["lastOprateTime"] = Date["now"]()),
        await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
          action: "set",
          id: _0x444868,
          newRow: {
            options: _0x40114f,
          },
        });
      if (await a0_0x295ba3("div", _0x59db49)) {
        await a0_0x2a2a71(0x898, 0xaf0);
        var _0x40997f = await a0_0x25b9e7();
        return _0x40997f || false;
      }
      return false;
    } catch (_0x179cfd) {}
  },
  yjSwipe: async (_0xe4d498) => {
    try {
      var _0x1b3ac3 = (_0xe4d498 =
          "string" == typeof _0xe4d498 ? JSON["parse"](_0xe4d498) : _0xe4d498)[
          "type"
        ],
        _0x19edbe = _0xe4d498["rowId"];
      let _0xc493a8 = {
        swipe: "仅观看滑动",
        dianzan: "点赞",
        pinglun: "评论",
        shoucan: "收藏",
      };
      var _0x6fc164 = a0_0x3c2a88(0xbb8, 0x2710),
        _0x4c8f93 = (_0x6fc164 / 0x3e8)["toFixed"](0x1);
      let _0x42071a = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x19edbe,
        },
      );
      var _0x289cbd = null == _0x42071a ? undefined : _0x42071a["options"];
      if (
        (_0x289cbd &&
          ((_0x289cbd["status"] =
            "养号-" + _0xc493a8[_0x1b3ac3] + "-停留 " + _0x4c8f93 + "-秒"),
          (_0x289cbd["lastOprateTime"] = Date["now"]()),
          await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
            action: "set",
            id: _0x19edbe,
            newRow: {
              options: _0x289cbd,
            },
          })),
        await ((_0x3a8dbc = _0x6fc164),
        new Promise((_0x2bc33e) => {
          setTimeout(_0x2bc33e, _0x3a8dbc);
        })),
        "dianzan" == _0x1b3ac3 &&
          (_0x42071a["bandDianzanNum"]
            ? a0_0x57b9b3("无法点赞，不操作！")
            : (document["dispatchEvent"](
                new KeyboardEvent("keydown", {
                  key: "z",
                  code: "KeyZ",
                  bubbles: true,
                }),
              ),
              await a0_0x2a2a71(0x1e, 0x78),
              document["dispatchEvent"](
                new KeyboardEvent("keyup", {
                  key: "z",
                  code: "KeyZ",
                  bubbles: true,
                }),
              ),
              await a0_0x2a2a71(0x4b0, 0x708))),
        "pinglun" == _0x1b3ac3)
      ) {
        if (_0x42071a["bandPinglunNum"]) a0_0x57b9b3("无法评论，不操作！");
        else {
          document["dispatchEvent"](
            new KeyboardEvent("keydown", {
              key: "x",
              code: "KeyX",
              bubbles: true,
            }),
          ),
            await a0_0x2a2a71(0x1e, 0x78),
            document["dispatchEvent"](
              new KeyboardEvent("keyup", {
                key: "x",
                code: "KeyX",
                bubbles: true,
              }),
            ),
            await a0_0x2a2a71(0x898, 0xaf0);
          const _0x257dec = document["querySelector"](
            ".comment-input-inner-container",
          );
          if (!_0x257dec) return;
          const _0x4a1879 = _0x257dec["getBoundingClientRect"](),
            _0x53be41 = (_0x3c2d10, _0x14fd07) =>
              Math["random"]() * (_0x14fd07 - _0x3c2d10) + _0x3c2d10,
            _0x3c6c89 =
              _0x4a1879["left"] +
              _0x4a1879["width"] / 0x2 +
              _0x53be41(-0xa, 0xa),
            _0x14c34f =
              _0x4a1879.top + _0x4a1879["height"] / 0x2 + _0x53be41(-0x5, 0x5);
          ["mousedown", "mouseup", "click"]["forEach"]((_0x5f58b2) => {
            _0x257dec["dispatchEvent"](
              new MouseEvent(_0x5f58b2, {
                bubbles: true,
                cancelable: true,
                view: window,
                clientX: _0x3c6c89,
                clientY: _0x14c34f,
              }),
            );
          }),
            await a0_0x2a2a71(0x4b0, 0x708);
          const _0x1eaac2 = document["querySelector"](
            '.public-DraftEditor-content[contenteditable="true"]',
          );
          if (!_0x1eaac2) return;
          _0x1eaac2["focus"](),
            await a0_0x2a2a71(0x4b0, 0x708),
            await a0_0x551e94["ipcRenderer"]["invoke"]("pasteComment", {
              id: _0x19edbe,
              comment: "很棒",
            }),
            await a0_0x2a2a71(0x898, 0xaf0);
        }
      }
      "shoucan" == _0x1b3ac3 &&
        (_0x42071a["bandShoucanNum"]
          ? a0_0x57b9b3("无法收藏，不操作！")
          : (document["dispatchEvent"](
              new KeyboardEvent("keydown", {
                key: "c",
                code: "KeyC",
                bubbles: true,
              }),
            ),
            await a0_0x2a2a71(0x1e, 0x78),
            document["dispatchEvent"](
              new KeyboardEvent("keyup", {
                key: "c",
                code: "KeyC",
                bubbles: true,
              }),
            ),
            await a0_0x2a2a71(0x320, 0x640))),
        await a0_0x2a2a71(0x4b0, 0x708);
      const _0x841408 = new KeyboardEvent("keydown", {
        key: "ArrowDown",
        code: "ArrowDown",
        keyCode: 0x28,
        which: 0x28,
        bubbles: true,
        cancelable: true,
      });
      return document["dispatchEvent"](_0x841408), true;
    } catch (_0x1c66d1) {}
    var _0x3a8dbc;
  },
  goFansPage: async (_0x128d71) => {
    try {
      var _0x4d06d2 = sessionStorage["getItem"]("WIN_ID");
      let _0x390e26 = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x4d06d2,
        },
      );
      var _0xafedfc = null == _0x390e26 ? undefined : _0x390e26["options"];
      return (
        (_0xafedfc["status"] = "进入粉丝主页"),
        (_0xafedfc["lastOprateTime"] = Date["now"]()),
        await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
          action: "set",
          id: _0x4d06d2,
          newRow: {
            options: _0xafedfc,
          },
        }),
        await a0_0x3ea95c(_0x128d71),
        true
      );
    } catch (_0x36f20f) {}
  },
  FansPageOprate: async (_0x110d1b) => {
    try {
      var _0x2c1d73 = (_0x110d1b =
        "string" == typeof _0x110d1b ? JSON["parse"](_0x110d1b) : _0x110d1b)[
        "id"
      ];
      let _0x225a11 = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x2c1d73,
        },
      );
      var _0x5bfd48 =
          (null == _0x225a11 ? undefined : _0x225a11["options"]) ||
          _0x110d1b["options"],
        _0x5ce48a = await (async function (_0x150a05, _0x5202c6) {
          try {
            _0x5202c6 = _0x5202c6 || {};
            var _0x2e6c76 = 0x0,
              _0x5b4b44 = 0x0;
            for (var _0x5e54da in _0x150a05)
              (_0x2e6c76 += _0x150a05[_0x5e54da]["total"]),
                (_0x5b4b44 += _0x150a05[_0x5e54da]["cur"]);
            if (
              "false" !== sessionStorage["getItem"]("isFirstSelect") &&
              _0x150a05["pinglun"]
            )
              return (
                sessionStorage["setItem"]("isFirstSelect", "false"),
                (_0x150a05["pinglun"]["cur"] += 0x1),
                {
                  newState: _0x150a05,
                  selectedModule: "pinglun",
                }
              );
            var _0x69d27 = _0x5b4b44 / _0x2e6c76,
              _0x3c6df9 = [],
              _0x3e6c13 = 0x0;
            for (var _0x5e54da in _0x150a05) {
              var _0x30d1fe = _0x150a05[_0x5e54da]["cur"],
                _0x59d330 = _0x150a05[_0x5e54da]["total"],
                _0x36db11 = _0x30d1fe / _0x59d330;
              _0x30d1fe >= _0x59d330 ||
                ("tuwen" === _0x5e54da && true !== _0x5202c6["tuwen"]) ||
                (_0x5202c6[_0x5e54da] && _0x36db11 < _0x69d27
                  ? (_0x3c6df9["push"]({
                      name: _0x5e54da,
                      weight: 0x2 * _0x59d330,
                    }),
                    (_0x3e6c13 += 0x2 * _0x59d330))
                  : (_0x3c6df9["push"]({
                      name: _0x5e54da,
                      weight: _0x59d330,
                    }),
                    (_0x3e6c13 += _0x59d330)));
            }
            if (0x0 === _0x3c6df9["length"])
              return {
                newState: _0x150a05,
                selectedModule: null,
              };
            for (
              var _0x36fc5f = Math["random"]() * _0x3e6c13,
                _0x5723f1 = 0x0,
                _0x492f8a = null,
                _0xf324f9 = 0x0;
              _0xf324f9 < _0x3c6df9["length"];
              _0xf324f9++
            )
              if (_0x36fc5f <= (_0x5723f1 += _0x3c6df9[_0xf324f9]["weight"])) {
                _0x492f8a = _0x3c6df9[_0xf324f9]["name"];
                break;
              }
            return (
              _0x492f8a && (_0x150a05[_0x492f8a]["cur"] += 0x1),
              {
                newState: _0x150a05,
                selectedModule: _0x492f8a,
              }
            );
          } catch (_0x1f7023) {}
        })(_0x5bfd48["tongjiNum"], {});
      let _0x3a0be7 = {
        guanzhu: "关注",
        dianzan: "点赞",
        pinglun: "评论",
        shoucan: "收藏",
      };
      _0x5ce48a["selectedModule"] &&
        ((_0x5bfd48["status"] =
          "打开作品-" + _0x3a0be7[_0x5ce48a["selectedModule"]]),
        (_0x5bfd48["lastOprateTime"] = Date["now"]()),
        await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
          action: "set",
          id: _0x2c1d73,
          newRow: {
            options: _0x5bfd48,
          },
        }));
      var _0x2e1b00 = await a0_0x12215b();
      if ((await a0_0x2a2a71(0x898, 0xaf0), _0x2e1b00)) {
        const _0x4e993d = (function (_0x11e5d0) {
          if (!(_0x11e5d0 instanceof SVGElement)) return "all";
          const _0x2a4c0c = _0x11e5d0["querySelector"]("mask"),
            _0x15298a = _0x11e5d0["querySelector"]("g[mask]");
          if (
            _0x2a4c0c &&
            _0x15298a &&
            _0x15298a["querySelector"]("circle") &&
            _0x15298a["querySelector"]("path")
          )
            return "female";
          const _0x40dfd5 = _0x11e5d0["querySelector"]("path[d]");
          return _0x40dfd5 &&
            "M8 1.25a.75.75 0 0 0 0 1.5h1.09L7.54 4.298a.757.757 0 0 0-.058.066 4 4 0 1 0 .968 1.112.752.752 0 0 0 .15-.117L10.25 3.71V5a.75.75 0 0 0 1.5 0V2a.75.75 0 0 0-.75-.75H8zM5 10a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z" ===
              _0x40dfd5["getAttribute"]("d")
            ? "male"
            : "all";
        })(document["querySelector"]('svg[width="12"][height="12"]'));
        await a0_0x551e94["ipcRenderer"]["invoke"]("gender-check", {
          gender: _0x4e993d,
        });
        let _0x10296c = false;
        if (
          (("all" == _0x225a11["gender"] || _0x225a11["gender"] == _0x4e993d) &&
            (_0x10296c = true),
          await a0_0x2a2a71(0x320, 0x4b0),
          _0x10296c)
        ) {
          await a0_0x7c1ac8(_0x2e1b00),
            await a0_0x17eda6(),
            await a0_0x2a2a71(0x320, 0x4b0);
          const _0x3d3cbc = (function () {
            const _0xcc9d6 = Array["from"](
                document["querySelectorAll"]('[data-e2e="video-player-digg"]'),
              ),
              _0x2bbc96 =
                _0xcc9d6["filter"]((_0x5b7802) => {
                  const _0x785561 = _0x5b7802["getBoundingClientRect"]();
                  return (
                    _0x785561["width"] > 0x0 &&
                    _0x785561["height"] > 0x0 &&
                    _0x785561.top >= 0x0 &&
                    _0x785561["bottom"] <=
                      (window["innerHeight"] ||
                        document["documentElement"]["clientHeight"])
                  );
                })[0x0] || _0xcc9d6[0x0];
            if (!_0x2bbc96) return null;
            const _0x5233bc = _0x2bbc96["textContent"]["trim"](),
              _0x179268 = _0x5233bc["match"](/(\d+(\.\d+)?)\s*[wW万]/);
            if (_0x179268)
              return Math["round"](0x2710 * parseFloat(_0x179268[0x1]));
            const _0x45198c = _0x5233bc["match"](/\d+/);
            return _0x45198c ? parseInt(_0x45198c[0x0], 0xa) : 0x0;
          })();
          if (
            (await a0_0x551e94["ipcRenderer"]["invoke"]("dzs-check", {
              dzs: _0x3d3cbc,
            }),
            Number(_0x3d3cbc) <= _0x225a11["maxLikeCountForDianzan"])
          ) {
            var _0x2e7f5 = 0x3e8 * Number(_0x225a11["enterWorkWaitSeconds"]),
              _0x3f67e2 = Math["max"](0x0, _0x2e7f5 - 0x3e8),
              _0x4e1086 = _0x2e7f5 + 0x3e8;
            if (
              (await a0_0x2a2a71(_0x3f67e2, _0x4e1086),
              "guanzhu" == _0x5ce48a["selectedModule"] &&
                (document["dispatchEvent"](
                  new KeyboardEvent("keydown", {
                    key: "g",
                    code: "KeyG",
                    bubbles: true,
                  }),
                ),
                await a0_0x2a2a71(0x1e, 0x78),
                document["dispatchEvent"](
                  new KeyboardEvent("keyup", {
                    key: "g",
                    code: "KeyG",
                    bubbles: true,
                  }),
                ),
                await a0_0x2a2a71(0x4b0, 0x708)),
              "dianzan" == _0x5ce48a["selectedModule"] &&
                (_0x225a11["bandDianzanNum"]
                  ? a0_0x57b9b3("无法点赞，不操作！")
                  : (document["dispatchEvent"](
                      new KeyboardEvent("keydown", {
                        key: "z",
                        code: "KeyZ",
                        bubbles: true,
                      }),
                    ),
                    await a0_0x2a2a71(0x1e, 0x78),
                    document["dispatchEvent"](
                      new KeyboardEvent("keyup", {
                        key: "z",
                        code: "KeyZ",
                        bubbles: true,
                      }),
                    ),
                    await a0_0x2a2a71(0x4b0, 0x708))),
              "pinglun" == _0x5ce48a["selectedModule"])
            ) {
              if (_0x225a11["bandPinglunNum"])
                a0_0x57b9b3("无法评论，不操作！");
              else {
                document["dispatchEvent"](
                  new KeyboardEvent("keydown", {
                    key: "x",
                    code: "KeyX",
                    bubbles: true,
                  }),
                ),
                  await a0_0x2a2a71(0x1e, 0x78),
                  document["dispatchEvent"](
                    new KeyboardEvent("keyup", {
                      key: "x",
                      code: "KeyX",
                      bubbles: true,
                    }),
                  ),
                  await a0_0x2a2a71(0x898, 0xaf0);
                const _0x301924 = document["querySelector"](
                  ".comment-input-inner-container",
                );
                if (!_0x301924) return;
                const _0x319b93 = _0x301924["getBoundingClientRect"](),
                  _0x167987 = (_0x2db12f, _0x391957) =>
                    Math["random"]() * (_0x391957 - _0x2db12f) + _0x2db12f,
                  _0x4f06f9 =
                    _0x319b93["left"] +
                    _0x319b93["width"] / 0x2 +
                    _0x167987(-0xa, 0xa),
                  _0x40f615 =
                    _0x319b93.top +
                    _0x319b93["height"] / 0x2 +
                    _0x167987(-0x5, 0x5);
                ["mousedown", "mouseup", "click"]["forEach"]((_0x28ef1a) => {
                  _0x301924["dispatchEvent"](
                    new MouseEvent(_0x28ef1a, {
                      bubbles: true,
                      cancelable: true,
                      view: window,
                      clientX: _0x4f06f9,
                      clientY: _0x40f615,
                    }),
                  );
                }),
                  await a0_0x2a2a71(0x4b0, 0x708);
                const _0x56ad10 = document["querySelector"](
                  '.public-DraftEditor-content[contenteditable="true"]',
                );
                if (!_0x56ad10) return;
                _0x56ad10["focus"](),
                  await a0_0x2a2a71(0x4b0, 0x708),
                  await a0_0x551e94["ipcRenderer"]["invoke"]("pasteComment", {
                    id: _0x110d1b["id"],
                    comment: "很棒",
                  }),
                  await a0_0x2a2a71(0x4b0, 0x708);
              }
            }
            "shoucan" == _0x5ce48a["selectedModule"] &&
              (_0x225a11["bandShoucanNum"]
                ? a0_0x57b9b3("无法收藏，不操作！")
                : (document["dispatchEvent"](
                    new KeyboardEvent("keydown", {
                      key: "c",
                      code: "KeyC",
                      bubbles: true,
                    }),
                  ),
                  await a0_0x2a2a71(0x1e, 0x78),
                  document["dispatchEvent"](
                    new KeyboardEvent("keyup", {
                      key: "c",
                      code: "KeyC",
                      bubbles: true,
                    }),
                  ),
                  await a0_0x2a2a71(0x320, 0x640))),
              null == _0x5ce48a["selectedModule"] &&
              _0x5bfd48 &&
              _0x5bfd48["tongjiNum"]
                ? (Object["keys"](_0x5bfd48["tongjiNum"])["forEach"](
                    (_0x4752e5) => {
                      _0x5bfd48["tongjiNum"][_0x4752e5] &&
                        "object" == typeof _0x5bfd48["tongjiNum"][_0x4752e5] &&
                        (_0x5bfd48["tongjiNum"][_0x4752e5]["cur"] = 0x0);
                    },
                  ),
                  (_0x5bfd48["status"] = "当前总任务数完成-进行重置"),
                  (_0x5bfd48["lastOprateTime"] = Date["now"]()),
                  await a0_0x551e94["ipcRenderer"]["invoke"](
                    "account-row-action",
                    {
                      action: "set",
                      id: _0x2c1d73,
                      newRow: {
                        options: _0x5bfd48,
                      },
                    },
                  ))
                : (_0x5bfd48["tongjiNum"] = _0x5ce48a["newState"]);
            const _0x3b8ac1 = await a0_0x1b7b17();
            _0x5bfd48 &&
              _0x5bfd48["tongjiNum"] &&
              ((_0x5bfd48["guanzhuNum"] = _0x3b8ac1["followCount"]),
              (_0x5bfd48["fensiNum"] = _0x3b8ac1["fansCount"]),
              (_0x5bfd48["userName"] = _0x3b8ac1["username"]),
              (_0x5bfd48["jintianFans"] =
                Number(_0x5bfd48["fensiNum"]) -
                Number(_0x5bfd48["todayStartFans"]))),
              (_0x5bfd48["lastOprateTime"] = Date["now"]()),
              await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
                action: "set",
                id: _0x2c1d73,
                newRow: {
                  options: _0x5bfd48,
                },
              });
            var _0x5837a9 = 0x3e8 * Number(_0x225a11["afterOprWaitSeconds"]),
              _0x57139c = Math["max"](0x0, _0x5837a9 - 0x5dc),
              _0x552ace = _0x5837a9 + 0x5dc;
            await a0_0x2a2a71(_0x57139c, _0x552ace);
          } else a0_0x57b9b3("作品点赞数大于设置，不操作！");
        } else a0_0x57b9b3("性别不符合，不操作！");
        return true;
      }
      return false;
    } catch (_0x3aa2a9) {}
  },
});
const a0_0x12215b = async () => {
    try {
      let _0x52b496 = 0x0,
        _0x520aa6 = null;
      for (; _0x52b496 < 0x5; ) {
        const _0x1f6122 = document["querySelectorAll"]("*");
        if (
          ((_0x520aa6 = Array["from"](_0x1f6122)["find"]((_0x3c6ca9) =>
            _0x3c6ca9["getAttributeNames"]()["some"](
              (_0x5c1033) =>
                _0x5c1033["startsWith"]("data-") &&
                "scroll-list" === _0x3c6ca9["getAttribute"](_0x5c1033),
            ),
          )),
          _0x520aa6)
        )
          break;
        _0x52b496++, await a0_0x2a2a71(0x3e8, 0x5dc);
      }
      if (!_0x520aa6) return false;
      let _0x14984a = _0x520aa6["querySelectorAll"]("li");
      for (_0x52b496 = 0x0; _0x52b496 < 0x3 && !(_0x14984a["length"] > 0x0); )
        _0x52b496++,
          await a0_0x2a2a71(0x3e8, 0x5dc),
          (_0x14984a = _0x520aa6["querySelectorAll"]("li"));
      if (0x0 === _0x14984a["length"]) return false;
      const _0x32a59d = Array["from"](_0x14984a)["slice"](0x0, 0x3);
      if (0x0 === _0x32a59d["length"]) return false;
      const _0x1e0a56 =
        _0x32a59d[Math["floor"](Math["random"]() * _0x32a59d["length"])];
      return _0x1e0a56["querySelector"]("a");
    } catch (_0x23f77d) {}
  },
  a0_0x7c1ac8 = async (_0x3bd697) => {
    try {
      const _0x532d47 = new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
        view: window,
      });
      _0x3bd697["dispatchEvent"](_0x532d47);
    } catch (_0x314f42) {}
  },
  a0_0x25b9e7 = async () => {
    try {
      const _0x262286 = Array["from"](document["querySelectorAll"]("div"))[
        "find"
      ]((_0xc83fc3) => "粉丝" == _0xc83fc3["textContent"]);
      if (_0x262286) {
        const _0x48b877 = _0x262286["closest"]("div");
        if (_0x48b877) {
          await a0_0x2a2a71(0x898, 0xaf0);
          var _0x466b42 = await a0_0xb3fc43(_0x48b877);
          return _0x466b42 || false;
        }
        return false;
      }
      return false;
    } catch (_0x44c741) {}
  },
  a0_0xb3fc43 = async (_0x4c95f0) => {
    try {
      let _0x216b0d = 0x0,
        _0x28c567 = null;
      for (; _0x216b0d < 0x5; ) {
        await a0_0x7c1ac8(_0x4c95f0), await a0_0x2a2a71(0x898, 0xaf0);
        const _0x68ce40 = document["querySelectorAll"]("*");
        if (
          ((_0x28c567 = Array["from"](_0x68ce40)["find"]((_0x4c8fae) =>
            _0x4c8fae["getAttributeNames"]()["some"](
              (_0x1d9a0e) =>
                _0x1d9a0e["startsWith"]("data-") &&
                "user-fans-container" === _0x4c8fae["getAttribute"](_0x1d9a0e),
            ),
          )),
          _0x28c567)
        )
          break;
        _0x216b0d++, await a0_0x2a2a71(0x3e8, 0x5dc);
      }
      if (!_0x28c567) return false;
      var _0x521530 = _0x28c567["scrollHeight"];
      let _0x18bb9c = 0x0,
        _0x39fa6a = _0x521530 - _0x28c567["scrollTop"];
      var _0x50fc63 = sessionStorage["getItem"]("WIN_ID");
      let _0x42cce2 = await a0_0x551e94["ipcRenderer"]["invoke"](
          "account-row-action",
          {
            action: "get",
            id: _0x50fc63,
          },
        ),
        _0x32b729 = Math["floor"](0x12d * Math["random"]()) + 0x1f4;
      (function () {
        const _0x3d1003 = "firstTimeGetFlag",
          _0x21f3af = Date["now"](),
          _0xf69e53 = localStorage["getItem"](_0x3d1003);
        return _0xf69e53
          ? _0x21f3af - parseInt(_0xf69e53) > 0x5265c00 &&
              (localStorage["setItem"](_0x3d1003, _0x21f3af), true)
          : (localStorage["setItem"](_0x3d1003, _0x21f3af), true);
      })() && (_0x32b729 = 0x64),
        0x0 == _0x42cce2["runMode"] && (_0x32b729 = a0_0x3c2a88(0xd, 0x19));
      const _0x5d0a06 = new Set();
      for (; _0x28c567["scrollTop"] + _0x28c567["clientHeight"] < _0x521530; ) {
        (_0x39fa6a = _0x521530 - _0x28c567["scrollTop"]),
          _0x28c567["clientHeight"];
        let _0xe67bb9 = _0x39fa6a * (0.3 * Math["random"]() + 0.6);
        _0xe67bb9 = Math["min"](_0xe67bb9, 0x12c);
        const _0x532f8a = 0x3e8 * Math["random"]() + 0x3e8;
        (_0x28c567["scrollTop"] += _0xe67bb9),
          await a0_0x2a2a71(_0x532f8a - 0x1f4, _0x532f8a + 0x1f4),
          (_0x50fc63 = sessionStorage["getItem"]("WIN_ID"));
        let _0x276876 = await a0_0x551e94["ipcRenderer"]["invoke"](
          "account-row-action",
          {
            action: "get",
            id: _0x50fc63,
          },
        );
        var _0x326d4c = null == _0x276876 ? undefined : _0x276876["options"];
        (_0x326d4c["lastOprateTime"] = Date["now"]()),
          await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
            action: "set",
            id: _0x50fc63,
            newRow: {
              options: _0x326d4c,
            },
          }),
          Array["from"](_0x28c567["querySelectorAll"]("a"))["forEach"](
            (_0x13c57e) => {
              const _0x52cd35 = _0x13c57e["getAttribute"]("href");
              _0x52cd35 &&
                _0x5d0a06["add"](
                  "https:" +
                    _0x52cd35 +
                    "?from_tab_name=main&is_search=0&list_name=fans&nt=0",
                );
            },
          );
        const _0xf8d950 = _0x28c567["scrollHeight"];
        if (
          (_0xf8d950 !== _0x521530 && (_0x521530 = _0xf8d950),
          _0x5d0a06["size"] >= _0x32b729 ||
            _0x28c567["scrollTop"] + _0x28c567["clientHeight"] >= _0x521530)
        )
          break;
        _0x18bb9c = _0x28c567["scrollTop"];
      }
      var _0x1f0827 = Array["from"](_0x5d0a06);
      return (
        0x0 == _0x42cce2["runMode"] &&
          (_0x1f0827 = _0x1f0827["slice"](0x0, _0x32b729)),
        _0x1f0827
      );
    } catch (_0x459cd0) {}
  },
  a0_0x2b2e45 = async (_0x33d9e0) => {
    try {
      const _0x45045b = await a0_0x1b7b17();
      _0x45045b && _0x45045b["username"]
        ? a0_0x551e94["ipcRenderer"]["send"]("login-status", true)
        : (await a0_0x2a2a71(0x898, 0xaf0), await a0_0x2b2e45(_0x33d9e0));
    } catch (_0x4ce1b1) {}
  },
  a0_0x1b7b17 = async () => {
    try {
      const _0x4002b4 = {
          followCount: null,
          fansCount: null,
          username: null,
        },
        _0x4b4bde = document["querySelector"]('[id*="douyin-header-menu"]');
      if (_0x4b4bde) {
        const _0x21e116 = Array["from"](
          _0x4b4bde["getElementsByTagName"]("span"),
        )["find"]((_0x23a3b7) => "关注" === _0x23a3b7["textContent"]);
        _0x21e116 &&
          (_0x4002b4["followCount"] = _0x21e116["nextElementSibling"]
            ? _0x21e116["nextElementSibling"]["textContent"]["trim"]()
            : null);
        const _0x3e8e94 = Array["from"](
          _0x4b4bde["getElementsByTagName"]("span"),
        )["find"]((_0x1ba4e7) => "粉丝" === _0x1ba4e7["textContent"]);
        _0x3e8e94 &&
          (_0x4002b4["fansCount"] = _0x3e8e94["nextElementSibling"]
            ? _0x3e8e94["nextElementSibling"]["textContent"]["trim"]()
            : null);
        const _0x42d8ef = _0x21e116 ? _0x21e116["closest"]("div") : null;
        if (_0x42d8ef) {
          const _0x5a2209 = _0x42d8ef["parentElement"]["querySelector"]("a");
          _0x5a2209 &&
            (_0x4002b4["username"] = _0x5a2209["textContent"]["trim"]());
        }
      }
      return _0x4002b4;
    } catch (_0x54a675) {}
  },
  a0_0x25e2c8 = async (_0x136b66) => {
    try {
      window["location"]["replace"](
        "https://www.douyin.com/search/" + _0x136b66,
      );
    } catch (_0x56b87a) {}
  },
  a0_0x3ea95c = async (_0xb75187) => {
    try {
      window["location"]["replace"](_0xb75187);
    } catch (_0x51e984) {}
  },
  a0_0x295ba3 = async (_0x11f9a6, _0xdaa78a, _0x277155 = 0xa) => {
    try {
      let _0x33a17f = _0x277155;
      for (; _0x33a17f > 0x0; ) {
        const _0x4d12ee = document["querySelectorAll"](_0x11f9a6);
        let _0x30d8f7 = false;
        if (
          (Array["from"](_0x4d12ee)["forEach"]((_0x1fda44) => {
            _0x1fda44["textContent"]["includes"](_0xdaa78a) &&
              (_0x30d8f7 = true);
          }),
          _0x30d8f7)
        )
          return true;
        await a0_0x2a2a71(0x7d0, 0xbb8), _0x33a17f--;
      }
      return false;
    } catch (_0x4be622) {}
  },
  a0_0x1dcfdf = async (_0x10a38d) => {
    try {
      const _0xd67a9e = document["querySelectorAll"]("span");
      let _0x1c2e0d = null;
      if (
        (_0xd67a9e["forEach"]((_0x1d5bd6) => {
          _0x1d5bd6["textContent"]["includes"](_0x10a38d) &&
            (_0x1c2e0d = _0x1d5bd6);
        }),
        !_0x1c2e0d)
      )
        return null;
      const _0x393f78 = _0x1c2e0d["closest"]("div"),
        _0x3dd27a = _0x393f78 ? _0x393f78["querySelector"]("a") : null;
      if (_0x3dd27a) return _0x3dd27a["getAttribute"]("href");
      return null;
    } catch (_0x19b53d) {}
  },
  { appendLoading: a0_0x5edf52, removeLoading: a0_0x3f166c } = (function () {
    const _0x30421d = "loaders-css__square-spin",
      _0x3012e5 =
        "\n@keyframes square-spin {\n  25% { transform: perspective(100px) rotateX(180deg) rotateY(0); }\n  50% { transform: perspective(100px) rotateX(180deg) rotateY(180deg); }\n  75% { transform: perspective(100px) rotateX(0) rotateY(180deg); }\n  100% { transform: perspective(100px) rotateX(0) rotateY(0); }\n}\n." +
        _0x30421d +
        " > div {\n  animation-fill-mode: both;\n  width: 50px;\n  height: 50px;\n  background: #fff;\n  animation: square-spin 3s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;\n}\n.app-loading-wrap {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #282c34;\n  z-index: 9;\n}\n    ",
      _0x1a2f1d = document["createElement"]("style"),
      _0x10bca4 = document["createElement"]("div");
    return (
      (_0x1a2f1d["id"] = "app-loading-style"),
      (_0x1a2f1d["innerHTML"] = _0x3012e5),
      (_0x10bca4["className"] = "app-loading-wrap"),
      (_0x10bca4["innerHTML"] =
        '<div class="' + _0x30421d + '"><div></div></div>'),
      {
        appendLoading() {
          a0_0x4ea7f6["append"](document["head"], _0x1a2f1d),
            a0_0x4ea7f6["append"](document["body"], _0x10bca4);
        },
        removeLoading() {
          a0_0x4ea7f6["remove"](document["head"], _0x1a2f1d),
            a0_0x4ea7f6["remove"](document["body"], _0x10bca4);
        },
      }
    );
  })();
a0_0x17eda6()["then"](a0_0x5edf52),
  (window["onmessage"] = (_0x2cdd6e) => {
    "removeLoading" === _0x2cdd6e["data"]["payload"] && a0_0x3f166c();
  }),
  setTimeout(a0_0x3f166c, 0x1387),
  a0_0x551e94["ipcRenderer"]["on"](
    "inject-fingerprint",
    (_0x1e3eb8, _0x1b92ad) => {
      try {
        !(function (_0x1cc209) {
          try {
            if (
              (_0x1cc209["userAgent"] &&
                Object["defineProperty"](navigator, "userAgent", {
                  get: () => _0x1cc209["userAgent"],
                  configurable: true,
                }),
              _0x1cc209["language"] &&
                (Object["defineProperty"](navigator, "language", {
                  get: () => _0x1cc209["language"],
                  configurable: true,
                }),
                Object["defineProperty"](navigator, "languages", {
                  get: () => [_0x1cc209["language"]],
                  configurable: true,
                })),
              _0x1cc209["screen"] &&
                (Object["defineProperty"](window["screen"], "width", {
                  get: () => _0x1cc209["screen"]["width"],
                  configurable: true,
                }),
                Object["defineProperty"](window["screen"], "height", {
                  get: () => _0x1cc209["screen"]["height"],
                  configurable: true,
                }),
                Object["defineProperty"](window["screen"], "availWidth", {
                  get: () => _0x1cc209["screen"]["width"],
                  configurable: true,
                }),
                Object["defineProperty"](window["screen"], "availHeight", {
                  get: () => _0x1cc209["screen"]["height"],
                  configurable: true,
                })),
              _0x1cc209["timezone"])
            )
              try {
                const _0x5c7bb0 = Intl["DateTimeFormat"];
                Intl["DateTimeFormat"] = class extends _0x5c7bb0 {
                  constructor(_0x20088f, _0x39efe0) {
                    ((_0x39efe0 = _0x39efe0 || {})["timeZone"] =
                      _0x1cc209["timezone"]),
                      super(_0x20088f, _0x39efe0);
                  }
                };
              } catch (_0x2bcb8f) {}
            if (
              (_0x1cc209["deviceMemory"] &&
                Object["defineProperty"](navigator, "deviceMemory", {
                  get: () => Number(_0x1cc209["deviceMemory"]),
                  configurable: true,
                }),
              _0x1cc209["hardwareConcurrency"] &&
                Object["defineProperty"](navigator, "hardwareConcurrency", {
                  get: () => Number(_0x1cc209["hardwareConcurrency"]),
                  configurable: true,
                }),
              _0x1cc209["geo"])
            ) {
              const [_0x1d2011, _0x1178c1] = _0x1cc209["geo"]
                  ["split"](",")
                  ["map"](Number),
                _0x5a037b = {
                  coords: {
                    latitude: _0x1d2011,
                    longitude: _0x1178c1,
                    accuracy: 0x32,
                    altitude: null,
                    altitudeAccuracy: null,
                    heading: null,
                    speed: null,
                  },
                  timestamp: Date["now"](),
                };
              (navigator["geolocation"]["getCurrentPosition"] = function (
                _0x38bebe,
                _0x5dca2d,
              ) {
                _0x38bebe(_0x5a037b);
              }),
                (navigator["geolocation"]["watchPosition"] = function (
                  _0x2ee6de,
                  _0x1210be,
                ) {
                  return _0x2ee6de(_0x5a037b), 0x1;
                });
            }
            if (_0x1cc209["webglVendor"] || _0x1cc209["webglRenderer"]) {
              const _0x57bf1e =
                WebGLRenderingContext["prototype"]["getParameter"];
              WebGLRenderingContext["prototype"]["getParameter"] = function (
                _0x593e78,
              ) {
                return _0x1cc209["webglVendor"] && 0x9245 === _0x593e78
                  ? _0x1cc209["webglVendor"]
                  : _0x1cc209["webglRenderer"] && 0x9246 === _0x593e78
                    ? _0x1cc209["webglRenderer"]
                    : _0x57bf1e["apply"](this, arguments);
              };
            }
            Object["defineProperty"](navigator, "webdriver", {
              get: () => false,
              configurable: true,
            });
          } catch (_0x57f6ac) {}
        })(_0x1b92ad);
      } catch (_0x37706c) {}
    },
  );
let a0_0x26dd2f = location["href"],
  a0_0x433908 = Date["now"]();
function a0_0xd849b2() {
  try {
    location["href"] !== a0_0x26dd2f &&
      ((a0_0x26dd2f = location["href"]), (a0_0x433908 = Date["now"]()));
  } catch (_0x534727) {}
}
a0_0x581314(window, "hashchange", a0_0xd849b2, undefined),
  a0_0x581314(window, "popstate", a0_0xd849b2, undefined),
  a0_0x23ed35(a0_0xd849b2, 0x3e8);
let a0_0xbff6da = false,
  a0_0x125990 = false,
  a0_0x22a430 = 0x0,
  a0_0x542255 = false;
function a0_0x28b687() {
  if (!a0_0x542255) {
    a0_0x542255 = true;
    try {
      !(function (_0x37760c, _0x2d716c, _0x11e1d7) {
        _0x37760c["observe"](_0x2d716c, _0x11e1d7),
          a0_0x564a57["push"](_0x37760c);
      })(
        new MutationObserver(async () => {
          !(async function () {
            try {
              const _0x1fa01b = document["querySelectorAll"](
                ".semi-button-content",
              );
              for (const _0x215359 of Array["from"](_0x1fa01b))
                if (
                  _0x215359 instanceof HTMLElement &&
                  "我知道了" === _0x215359["innerText"]["trim"]()
                ) {
                  await a0_0x7c1ac8(_0x215359);
                  break;
                }
            } catch (_0x16f6bd) {}
          })();
          const _0x3b44be = document["querySelector"](".second-verify-panel"),
            _0x1b1e3f = document["querySelector"]("#douyin-login-new-id"),
            _0x52e3be = Array["from"](document["querySelectorAll"]("iframe"))[
              "find"
            ](
              (_0x507f46) =>
                _0x507f46["src"] &&
                _0x507f46["src"]["includes"]("verifycenter/captcha"),
            ),
            _0x371de2 = !!(_0x3b44be || _0x52e3be || _0x1b1e3f);
          _0x1b1e3f &&
            !a0_0x125990 &&
            ((a0_0x125990 = true),
            setTimeout(() => {
              document["querySelector"]("#douyin-login-new-id") &&
                (!(function () {
                  const _0x43103d = document["cookie"]
                    ["split"](";")
                    ["reduce"]((_0x1e7808, _0x386f49) => {
                      const [_0x15db02, ..._0x62c70a] = _0x386f49["split"]("=");
                      return (
                        (_0x1e7808[_0x15db02["trim"]()] =
                          _0x62c70a["join"]("=")),
                        _0x1e7808
                      );
                    }, {});
                  return (
                    _0x43103d["passport_assist_user"] ||
                    "true" === _0x43103d["passport_fe_beating_status"]
                  );
                })()
                  ? (a0_0x551e94["ipcRenderer"]["invoke"]("login-check", {
                      isL: false,
                    }),
                    a0_0x551e94["ipcRenderer"]["send"](
                      "pauseAutomationDueToPopup",
                      {
                        id: a0_0x425725,
                      },
                    ))
                  : (a0_0x551e94["ipcRenderer"]["invoke"]("login-check", {
                      isL: true,
                    }),
                    a0_0x551e94["ipcRenderer"]["send"](
                      "resumeAutomationDueToPopup",
                      a0_0x425725,
                    ),
                    window["location"]["reload"]())),
                (a0_0x125990 = false);
            }, 0x2710)),
            await (async function (_0x21f583) {
              const _0x4e9534 = Date["now"]();
              if (!(_0x4e9534 - a0_0x22a430 < 0xbb8)) {
                if (((a0_0x22a430 = _0x4e9534), _0x21f583)) {
                  var _0x25493a = sessionStorage["getItem"]("WIN_ID");
                  let _0x54745c = await a0_0x551e94["ipcRenderer"]["invoke"](
                    "account-row-action",
                    {
                      action: "get",
                      id: _0x25493a,
                    },
                  );
                  if (
                    ((a0_0x433908 = Date["now"]()),
                    !_0x54745c["isPopup"] &&
                      (_0x54745c &&
                        ((_0x54745c["isPopup"] = true),
                        sessionStorage["setItem"](
                          "row_" + _0x25493a,
                          JSON["stringify"](_0x54745c),
                        )),
                      a0_0x425725))
                  ) {
                    let _0x1b13a6 = false;
                    document["querySelector"]("#douyin-login-new-id") &&
                      (_0x1b13a6 = true),
                      a0_0x551e94["ipcRenderer"]["send"](
                        "pauseAutomationDueToPopup",
                        {
                          id: a0_0x425725,
                          isNotSend: _0x1b13a6,
                        },
                      ),
                      (_0x25493a = sessionStorage["getItem"]("WIN_ID"));
                    let _0x1bfc87 = await a0_0x551e94["ipcRenderer"]["invoke"](
                      "account-row-action",
                      {
                        action: "get",
                        id: _0x25493a,
                      },
                    );
                    var _0x1394e5 =
                      null == _0x1bfc87 ? undefined : _0x1bfc87["options"];
                    (_0x1394e5["status"] =
                      "有弹窗需要处理，请扫码或拖动，完成后等待20秒自动恢复流程"),
                      await a0_0x551e94["ipcRenderer"]["invoke"](
                        "account-row-action",
                        {
                          action: "set",
                          id: _0x25493a,
                          newRow: {
                            options: _0x1394e5,
                          },
                        },
                      );
                  }
                } else {
                  _0x25493a = sessionStorage["getItem"]("WIN_ID");
                  let _0x4322c9 = sessionStorage["getItem"]("row_" + _0x25493a),
                    _0x55e02e = _0x4322c9 ? JSON["parse"](_0x4322c9) : null;
                  _0x55e02e &&
                    _0x55e02e["isPopup"] &&
                    a0_0x425725 &&
                    (a0_0x551e94["ipcRenderer"]["send"](
                      "resumeAutomationDueToPopup",
                      a0_0x425725,
                    ),
                    (_0x55e02e["isPopup"] = false),
                    sessionStorage["setItem"](
                      "row_" + _0x25493a,
                      JSON["stringify"](_0x55e02e),
                    ));
                }
              }
            })(_0x371de2);
          const _0x4e45cd = document["querySelector"]("#toastContainer");
          if (_0x4e45cd && !a0_0xbff6da) {
            let _0x179a70 = null;
            if (
              (_0x4e45cd["innerText"]["includes"]("点赞功能已封禁") ||
              _0x4e45cd["innerText"]["includes"]("点赞速度太快啦")
                ? (_0x179a70 = "dianzan")
                : _0x4e45cd["innerText"]["includes"]("收藏功能已封禁")
                  ? (_0x179a70 = "shoucang")
                  : _0x4e45cd["innerText"]["includes"]("评论功能受限") &&
                    (_0x179a70 = "pinglun"),
              _0x179a70 && ((a0_0xbff6da = true), a0_0x425725))
            ) {
              var _0x3e5171 = sessionStorage["getItem"]("WIN_ID");
              let _0x54cb91 = await a0_0x551e94["ipcRenderer"]["invoke"](
                "account-row-action",
                {
                  action: "get",
                  id: _0x3e5171,
                },
              );
              "dianzan" === _0x179a70
                ? (_0x54cb91["bandDianzanNum"] =
                    Number(_0x54cb91["bandDianzanNum"] || 0x0) + 0x1)
                : "shoucang" === _0x179a70
                  ? (_0x54cb91["bandShoucanNum"] =
                      Number(_0x54cb91["bandShoucanNum"] || 0x0) + 0x1)
                  : "pinglun" === _0x179a70 &&
                    (_0x54cb91["bandPinglunNum"] =
                      Number(_0x54cb91["bandPinglunNum"] || 0x0) + 0x1),
                await a0_0x551e94["ipcRenderer"]["invoke"](
                  "account-row-action",
                  {
                    action: "set",
                    id: _0x3e5171,
                    newRow: _0x54cb91,
                  },
                ),
                sessionStorage["setItem"]("WIN_dialog", "1");
            }
          }
          (_0x4e45cd &&
            (_0x4e45cd["innerText"]["includes"]("点赞功能已封禁") ||
              _0x4e45cd["innerText"]["includes"]("点赞速度太快啦") ||
              _0x4e45cd["innerText"]["includes"]("收藏功能已封禁") ||
              _0x4e45cd["innerText"]["includes"]("评论功能受限"))) ||
            !a0_0xbff6da ||
            (a0_0xbff6da = false);
        }),
        document["body"],
        {
          childList: true,
          subtree: true,
          attributes: true,
          characterData: true,
          attributeFilter: ["class"],
        },
      );
    } catch (_0x40a808) {}
  }
}
if ("loading" === document["readyState"])
  try {
    a0_0x581314(window, "DOMContentLoaded", a0_0x28b687, undefined);
  } catch (a0_0x4429a6) {}
else a0_0x28b687();
a0_0x23ed35(async () => {
  var _0x34627e;
  try {
    const _0x2366fe = sessionStorage["getItem"]("WIN_ID");
    if (!_0x2366fe) return;
    let _0x55ebbd = await a0_0x551e94["ipcRenderer"]["invoke"](
        "account-row-action",
        {
          action: "get",
          id: _0x2366fe,
        },
      ),
      _0x155cf1 =
        null ==
        (_0x34627e = null == _0x55ebbd ? undefined : _0x55ebbd["options"])
          ? undefined
          : _0x34627e["lastOprateTime"];
    if (!_0x155cf1)
      return (
        (_0x155cf1 = Date["now"]()),
        void (await a0_0x551e94["ipcRenderer"]["invoke"]("account-row-action", {
          action: "set",
          id: _0x2366fe,
          newRow: {
            options: {
              ..._0x55ebbd["options"],
              lastOprateTime: _0x155cf1,
            },
          },
        }))
      );
    if (Date["now"]() - _0x155cf1 > 0x7530 && _0x55ebbd["isPaused"])
      return void (_0x155cf1 = Date["now"]());
  } catch (_0x3e4929) {}
}, 0x2710),
  a0_0x581314(
    document,
    "click",
    function (_0x81e71) {
      var _0x1b2b7d, _0x5fe523;
      const _0x35b900 =
        null ==
        (_0x5fe523 =
          null == (_0x1b2b7d = _0x81e71["target"])
            ? undefined
            : _0x1b2b7d["closest"])
          ? undefined
          : _0x5fe523["call"](_0x1b2b7d, "a");
      _0x35b900 &&
        "string" == typeof _0x35b900["href"] &&
        _0x35b900["href"]["startsWith"]("bitbrowser://") &&
        (_0x81e71["preventDefault"](), _0x81e71["stopPropagation"]());
    },
    true,
  ),
  a0_0x551e94["contextBridge"]["exposeInMainWorld"]("electronAPI", {
    getProxyIp: async function () {
      try {
        const _0x40090d = await fetch("http://ip-api.com/json");
        return (await _0x40090d["json"]())["query"];
      } catch (_0x2dbe7c) {
        return null;
      }
    },
    startPopupObserver: a0_0x28b687,
    checkPopup: () => !!document["querySelector"](".second-verify-panel"),
    getRow: (_0x2debc9) =>
      a0_0x551e94["ipcRenderer"]["invoke"]("get-account-row", _0x2debc9),
    updateRow: ({ id: _0x11d89c, newRow: _0x945421 }) =>
      a0_0x551e94["ipcRenderer"]["invoke"]("update-account-row", {
        id: _0x11d89c,
        newRow: _0x945421,
      }),
  }),
  a0_0x551e94["contextBridge"]["exposeInMainWorld"]("hardware", {
    getDeviceId: async () =>
      await a0_0x551e94["ipcRenderer"]["invoke"]("get-device-id"),
  }),
  a0_0x581314(
    window,
    "contextmenu",
    (_0x5731c3) => {
      _0x5731c3["preventDefault"]();
    },
    undefined,
  ),
  window["addEventListener"]("beforeunload", () => {
    a0_0x432ec1["forEach"](clearInterval),
      a0_0x564a57["forEach"]((_0x447e90) => _0x447e90["disconnect"]()),
      a0_0x3e8195["forEach"](
        ({
          target: _0x22e851,
          event: _0x292257,
          handler: _0x4c6f49,
          opts: _0xa2cbf9,
        }) => {
          _0x22e851["removeEventListener"](_0x292257, _0x4c6f49, _0xa2cbf9);
        },
      ),
      (a0_0x432ec1["length"] = 0x0),
      (a0_0x564a57["length"] = 0x0),
      (a0_0x3e8195["length"] = 0x0);
  });
