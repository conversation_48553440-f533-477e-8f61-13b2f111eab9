{"name": "wustart", "private": true, "version": "3.0.0", "type": "module", "main": "dist-electron/main/index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron:build": "npm run build && electron-builder", "electron:serve": "electron ."}, "dependencies": {"@nut-tree/nut-js": "^3.1.1", "axios": "^1.11.0", "crypto-js": "^4.2.0", "electron-store": "^8.1.0", "element-plus": "^2.10.5", "node-fetch": "^3.3.2", "node-machine-id": "^1.1.12", "proxy-chain": "^2.4.0", "user-agents": "^1.1.0", "vue": "^3.5.17"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^6.0.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "vite": "^7.0.4", "wait-on": "^7.2.0"}, "build": {"appId": "com.wustart.app", "productName": "超凡", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}