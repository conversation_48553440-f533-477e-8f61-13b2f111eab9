<script setup>
import { ref, reactive, onMounted, inject } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import IpSetting from './components/IpSetting.vue'
import AccountTable from './components/AccountTable.vue'

// 注入应用管理器
const appManager = inject('appManager')

// 基础状态
const activeTab = ref('accountSetting')
const licenseKey = ref('')
const isActivated = ref(false)
const checkingLicense = ref(false)
const expireDate = ref('')
const deviceId = ref('')

// 微信通知配置（按根 index.js 对齐：machineId + qqEmail + enabled）
const notifyConfig = reactive({
  machineId: '1',
  qqEmail: '',
  enabled: false
})

// 表单引用与校验规则
const notifyFormRef = ref(null)
const notifyRules = reactive({
  machineId: [{ required: true, message: '机器编号不能为空', trigger: 'blur' }],
  qqEmail: [
    { required: true, message: 'QQ邮箱不能为空', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur','change'] }
  ]
})

// 账号列表
const accounts = ref([])
const selectedAccounts = ref([])

// 批量配置
const batchConfig = reactive({
  guanzhu: 10,
  dianzan: 1500,
  pinglun: 180,
  shoucan: 150
})

// 全局配置
const globalConfig = reactive({
  enterWorkWaitSeconds: 6,
  afterOprWaitSeconds: 4,
  gender: 'all',
  maxLikeCountForDianzan: 999
})

// 弹窗控制
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const editIndex = ref(-1)

// 其他状态
const showCrackAlert = ref(false)
const onlyBaiduProxy = ref(false)

// 新增账号数据
const newAccount = reactive({
  id: '',
  ips: '未代理',
  runMode: 0,
  runHours: [],
  startTime: '08:00',
  endTime: '24:00'
})

// 编辑账号数据
const editAccountData = reactive({
  id: '',
  ips: '未代理',
  runMode: 0,
  runHours: [],
  startTime: '08:00',
  endTime: '24:00'
})

// 代理列表
const proxyList = ref([])

// 初始化IPC事件监听器（对应原始index.js的ge()函数逻辑）
const initializeIpcListeners = async () => {
  try {
    console.log('开始初始化IPC事件监听器...')

    // 设置默认核对IP状态为false（与原始index.js的ge()函数一致）
    window.ipcRenderer.send("set-only-baidu-proxy", false)

    // 监听账号行更新事件（与原始index.js完全一致的逻辑）
    window.ipcRenderer.on("account-row-updated", (event, { id, newRow }) => {
      console.log('App.vue收到账号行更新:', id, newRow)
      const accountIndex = accounts.value.findIndex(account => account.id === id)
      if (accountIndex !== -1) {
        // 完全按照原始index.js的更新逻辑
        accounts.value[accountIndex] = {
          ...accounts.value[accountIndex],
          ...newRow
        }
        console.log('App.vue账号数据已更新:', accounts.value[accountIndex])
      }
    })

    // 监听破解警告事件（从原始index.js提取）
    window.ipcRenderer.on("crack-alert", (event, data) => {
      console.log('收到破解警告:', data)
      showCrackAlert.value = true
      // 停止所有账号
      closeAllAccounts()
    })

    console.log('IPC事件监听器初始化完成')
  } catch (error) {
    console.error('初始化IPC事件监听器失败:', error)
  }
}

// 初始化应用
onMounted(async () => {
  try {
    // 获取应用状态（保留 appManager 初始化）
    const status = appManager.getAppStatus()
    isActivated.value = status.isLicenseValid
    expireDate.value = status.licenseExpireTime
    deviceId.value = status.deviceId

    // 加载账号列表
    accounts.value = appManager.accountManager.accounts

    // 加载代理列表
    proxyList.value = appManager.proxyManager.getFormattedProxyList()

    // 加载微信通知配置（按根 index.js）
    const wxCfg = await window.ipcRenderer.invoke('get-store', 'wxNotifyConfig')
    if (wxCfg && typeof wxCfg === 'object') {
      notifyConfig.machineId = wxCfg.machineId || '1'
      notifyConfig.qqEmail = wxCfg.qqEmail || ''
      notifyConfig.enabled = !!wxCfg.enabled
    }

    // 加载本地卡密与到期时间（按根 index.js）
    const savedLicense = await window.ipcRenderer.invoke('get-store', 'kami_v1')
    const savedExpire = await window.ipcRenderer.invoke('get-store', 'kami_expire_v1')
    if (savedLicense && savedExpire) {
      licenseKey.value = savedLicense
      expireDate.value = savedExpire
      isActivated.value = true

      // 修复：只有在卡密和到期时间都存在时才初始化IPC监听（与原始index.js逻辑一致）
      await initializeIpcListeners()
    }

    console.log('界面初始化完成')
  } catch (error) {
    console.error('界面初始化失败:', error)
    ElMessage.error('界面初始化失败: ' + error.message)
  }
})

// 激活卡密
const activateLicense = async () => {
  if (!licenseKey.value.trim()) {
    ElMessage.warning('请输入卡密')
    return
  }
  
  checkingLicense.value = true
  try {
    const result = await appManager.activateLicense(licenseKey.value.trim())
    isActivated.value = true
    expireDate.value = result.expireTime
    deviceId.value = result.deviceId
    ElMessage.success('卡密激活成功')
  } catch (error) {
    ElMessage.error('卡密激活失败: ' + error.message)
  } finally {
    checkingLicense.value = false
  }
}

// 显示新增账号对话框
const showAddAccount = () => {
  resetAddForm()
  showAddDialog.value = true
}

// 新增账号
const addAccount = async () => {
  if (!newAccount.id.trim()) {
    ElMessage.warning('请输入抖音ID')
    return
  }
  
  if (!newAccount.ips) {
    ElMessage.warning('请选择代理IP')
    return
  }
  
  try {
    await appManager.addAccount({
      ...newAccount,
      runHours: [...newAccount.runHours]
    })
    
    // 刷新账号列表
    accounts.value = appManager.accountManager.accounts
    showAddDialog.value = false
    ElMessage.success('账号添加成功')
  } catch (error) {
    ElMessage.error('添加账号失败: ' + error.message)
  }
}

// 编辑账号
const editAccount = (row, index) => {
  editIndex.value = index
  Object.assign(editAccountData, {
    id: row.id,
    ips: row.ips,
    runMode: row.runMode,
    runHours: [...(row.runHours || [])],
    startTime: row.startTime,
    endTime: row.endTime
  })
  showEditDialog.value = true
}

// 更新账号
const updateAccount = async () => {
  if (!editAccountData.id.trim()) {
    ElMessage.warning('请输入抖音ID')
    return
  }
  
  try {
    await appManager.updateAccount(editIndex.value, {
      ...editAccountData,
      runHours: [...editAccountData.runHours]
    })
    
    // 刷新账号列表
    accounts.value = appManager.accountManager.accounts
    showEditDialog.value = false
    ElMessage.success('账号更新成功')
  } catch (error) {
    ElMessage.error('更新账号失败: ' + error.message)
  }
}

// 删除账号
const deleteAccount = async (row, index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个账号吗？', '确认删除', {
      type: 'warning'
    })
    
    await appManager.deleteAccount(index)
    accounts.value = appManager.accountManager.accounts
    ElMessage.success('账号删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除账号失败: ' + error.message)
    }
  }
}

// 启动账号
const openAccount = async (row) => {
  const index = accounts.value.findIndex(acc => acc.id === row.id)
  if (index === -1) return
  
  try {
    await appManager.startAccount(index)
    ElMessage.success(`账号 ${row.id} 启动成功`)
  } catch (error) {
    ElMessage.error(`启动账号失败: ${error.message}`)
  }
}

// 暂停/恢复账号
const togglePause = async (row) => {
  const index = accounts.value.findIndex(acc => acc.id === row.id)
  if (index === -1) return
  
  try {
    if (row.isPaused) {
      await appManager.automationController.resumeAccount(row)
      ElMessage.success(`账号 ${row.id} 已恢复`)
    } else {
      await appManager.automationController.pauseAccount(row)
      ElMessage.success(`账号 ${row.id} 已暂停`)
    }
  } catch (error) {
    ElMessage.error(`操作失败: ${error.message}`)
  }
}

// 一键全启
const openAllAccounts = async () => {
  try {
    const accountIndexes = accounts.value.map((_, index) => index)
    await appManager.batchStartAccounts(accountIndexes)
    ElMessage.success('批量启动完成')
  } catch (error) {
    ElMessage.error('批量启动失败: ' + error.message)
  }
}

// 一键关闭
const closeAllAccounts = async () => {
  try {
    const accountIndexes = accounts.value.map((_, index) => index)
    await appManager.batchStopAccounts(accountIndexes)
    ElMessage.success('批量关闭完成')
  } catch (error) {
    ElMessage.error('批量关闭失败: ' + error.message)
  }
}

// 批量启动选中账号
const batchStartAccounts = async () => {
  if (selectedAccounts.value.length === 0) {
    ElMessage.warning('请先选择要启动的账号')
    return
  }
  
  try {
    const selectedIndexes = selectedAccounts.value.map(acc => 
      accounts.value.findIndex(item => item.id === acc.id)
    ).filter(index => index !== -1)
    
    await appManager.batchStartAccounts(selectedIndexes)
    ElMessage.success('批量启动完成')
  } catch (error) {
    ElMessage.error('批量启动失败: ' + error.message)
  }
}

// 应用批量配置（按根 index.js：直接更新每个选中账号 options，并调用 update-account-row）
const applyBatchConfig = async () => {
  if (selectedAccounts.value.length === 0) {
    ElMessage.warning('请先选择要配置的账号')
    return
  }

  try {
    const taskKeys = ['guanzhu','dianzan','pinglun','shoucan']
    for (const acc of selectedAccounts.value) {
      const idx = accounts.value.findIndex(item => item.id === acc.id)
      if (idx === -1) continue
      const row = accounts.value[idx]

      // 更新任务总量并清零当日计数
      for (const k of taskKeys) {
        row.options.tongjiNum[k].total = batchConfig[k]
        row.options.tongjiNum[k].cur = 0
      }
      // 更新全局配置
      row.enterWorkWaitSeconds = globalConfig.enterWorkWaitSeconds
      row.afterOprWaitSeconds = globalConfig.afterOprWaitSeconds
      row.gender = globalConfig.gender
      row.maxLikeCountForDianzan = globalConfig.maxLikeCountForDianzan

      // 调用主进程更新存储（深拷贝 options）
      await window.ipcRenderer.invoke('update-account-row', {
        id: row.id,
        newRow: { options: JSON.parse(JSON.stringify(row.options)) }
      })
    }

    ElMessage.success('批量配置应用成功')
  } catch (error) {
    ElMessage.error('批量配置失败: ' + error.message)
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedAccounts.value = selection
}

// 打开百度核对真实代理IP：勾选后对选中账号（无选择则对全部）打开检测窗口
const onBaiduVerifyToggle = (val) => {
  try { window.ipcRenderer?.send('set-only-baidu-proxy', !!val) } catch {}
  if (!val) return
  try {
    const targets = (selectedAccounts.value?.length ? selectedAccounts.value : accounts.value) || []
    let opened = 0
    for (const row of targets) {
      if (row?.ips && row.ips !== '未代理') {
        const payload = JSON.parse(JSON.stringify(row))
        window.ipcRenderer?.send('open-google-window', payload)
        opened++
      }
    }
    if (opened > 0) {
      ElMessage.info(`已为 ${opened} 个账号打开百度用于核对代理IP`)
    } else {
      ElMessage.warning('未找到可用代理的账号，未打开窗口')
    }
  } catch (e) {
    ElMessage.error('打开百度核对失败: ' + e.message)
  }
}

// 一键排序
const sortAllAccounts = () => {
  accounts.value.sort((a, b) => a.index - b.index)
  ElMessage.success('账号排序完成')
}

// 重置新增表单
const resetAddForm = () => {
  Object.assign(newAccount, {
    id: '',
    ips: '未代理',
    runMode: 0,
    runHours: [],
    startTime: '08:00',
    endTime: '24:00'
  })
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editAccountData, {
    id: '',
    ips: '未代理',
    runMode: 0,
    runHours: [],
    startTime: '08:00',
    endTime: '24:00'
  })
  editIndex.value = -1
}

// 切换微信通知（按根 index.js：开启时先校验，通过后发送 setNotifyConfig；关闭直接发送）
const toggleNotify = async () => {
  if (notifyConfig.enabled) {
    if (!notifyFormRef.value) return
    await notifyFormRef.value.validate((valid) => {
      if (valid) {
        window.ipcRenderer.send('setNotifyConfig', {
          machineId: notifyConfig.machineId,
          qqEmail: notifyConfig.qqEmail,
          enabled: true
        })
        ElMessage.success('通知已开启')
      } else {
        notifyConfig.enabled = false
        ElMessage.error('请正确填写机器编号和QQ邮箱')
      }
    })
  } else {
    window.ipcRenderer.send('setNotifyConfig', {
      machineId: notifyConfig.machineId,
      qqEmail: notifyConfig.qqEmail,
      enabled: false
    })
    ElMessage.info('通知已关闭')
  }
}

// 工具函数
const formatHour = (h) => `${h.toString().padStart(2, '0')}:00`

const getProxyLabel = (val) => {
  if (!val || val === '未代理') return '未代理'
  const proxy = proxyList.value.find(p => p.value === val)
  return proxy ? proxy.label : val
}

const formatRunHours = (runHours, startTime, endTime) => {
  if (!runHours || runHours.length === 0) {
    return `${startTime}-${endTime}`
  }
  
  const sortedHours = [...runHours].sort((a, b) => a - b)
  const ranges = []
  let start = sortedHours[0]
  let end = sortedHours[0]

  for (let i = 1; i <= sortedHours.length; i++) {
    if (sortedHours[i] === end + 1) {
      end = sortedHours[i]
    } else {
      ranges.push(start === end ? 
        `${start.toString().padStart(2, '0')}:00` : 
        `${start.toString().padStart(2, '0')}:00-${(end + 1).toString().padStart(2, '0')}:00`
      )
      start = sortedHours[i]
      end = sortedHours[i]
    }
  }

  return ranges.join(', ')
}
</script>

<template>
  <div class="app-container">
    <div class="tabs-row">
      <el-tabs
        v-model="activeTab"
        stretch
        size="small"
        type="card"
        style="width: 450px"
      >
        <el-tab-pane label="账号配置" name="accountSetting" size="small" />
        <el-tab-pane label="代理IP" name="ipSetting" size="small" />
        <el-tab-pane label="微信通知" name="wxNotifySetting" size="small" />
        <el-tab-pane label="运行详情" name="runSetting" size="small" />
      </el-tabs>

      <!-- 卡密验证区域 -->
      <div v-if="activeTab === 'accountSetting'" style="margin-left: auto">
        <div class="kami-bar">
          <el-input
            v-model="licenseKey"
            placeholder="输入卡密"
            type="password"
            style="width: 180px; margin-right: 8px"
            clearable
            show-password
          />
          <el-button
            type="primary"
            :loading="checkingLicense"
            @click="activateLicense"
          >
            {{ isActivated ? "已启动" : "启动" }}
          </el-button>
        </div>
        <div style="text-align: right">
          <span v-if="isActivated" class="kami-info">
            有效期：{{ expireDate }}
          </span>
        </div>
      </div>
    </div>

    <!-- 微信通知配置（按根 index.js 对齐） -->
    <div v-if="activeTab === 'wxNotifySetting'" class="wx-notify-setting-container">
      <el-form
        :model="notifyConfig"
        :rules="notifyRules"
        ref="notifyFormRef"
        style="margin-top: 30px"
        label-width="120px"
      >
        <el-form-item label="机器编号" prop="machineId">
          <el-input
            v-model="notifyConfig.machineId"
            placeholder="请填写机器编号"
            clearable
          />
        </el-form-item>

        <el-form-item label="QQ邮箱" prop="qqEmail">
          <el-input
            v-model="notifyConfig.qqEmail"
            placeholder="请填写 QQ 邮箱"
            clearable
          />
        </el-form-item>

        <el-form-item label="开启通知">
          <el-radio-group v-model="notifyConfig.enabled" @change="toggleNotify">
            <el-radio-button :label="false">关闭</el-radio-button>
            <el-radio-button :label="true">开启</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <span style="color: #e6a23c">
            上号阶段请勿开启，防止弹窗过多信息轰炸，等上号后半小时再开启最佳
          </span>
        </el-form-item>
      </el-form>
    </div>

    <!-- 代理IP配置 -->
    <div v-if="activeTab === 'ipSetting'" class="ip-setting-container">
      <IpSetting />
    </div>

    <!-- 运行详情 -->
    <div v-if="activeTab === 'runSetting'" class="run-setting-container">
      <AccountTable />
    </div>

    <!-- 账号配置区域 -->
    <div v-if="activeTab === 'accountSetting'" class="account-setting-container">
      <el-row style="margin-bottom: 10px">
        <el-button
          type="primary"
          :disabled="showCrackAlert || !isActivated"
          @click="showAddAccount"
        >
          新增抖音号
        </el-button>
        
        <el-button
          type="primary"
          :disabled="showCrackAlert || !isActivated"
          @click="openAllAccounts"
        >
          一键全启
        </el-button>
        
        <el-button
          :disabled="showCrackAlert || !isActivated"
          @click="closeAllAccounts"
        >
          一键关闭
        </el-button>
        
        <el-button
          :disabled="selectedAccounts.length === 0"
          @click="batchStartAccounts"
        >
          批量启动
        </el-button>
        
        <el-button
          :disabled="showCrackAlert || !isActivated"
          @click="sortAllAccounts"
        >
          一键排序
        </el-button>

        <div style="flex: 1"></div>

        <el-checkbox
          :disabled="showCrackAlert || !isActivated"
          v-model="onlyBaiduProxy"
          @change="onBaiduVerifyToggle"
        >
          打开百度核对-真实代理IP
        </el-checkbox>
      </el-row>

      <div class="table-scroll-area">
        <el-table
          :data="accounts"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          border
          size="small"
          height="100%"
        >
          <el-table-column type="selection" width="35" align="center" />
          <el-table-column prop="index" label="窗口" width="60" align="center" />
          <el-table-column prop="id" label="抖音id" align="center" />
          
          <el-table-column
            label="任务：关注|点赞|评论|收藏|进入等秒|操作等秒|性别|点赞少于"
            width="250"
            align="center"
          >
            <template #default="{ row }">
              <span>{{ row.options?.tongjiNum?.guanzhu?.total || 0 }}</span> |
              <span>{{ row.options?.tongjiNum?.dianzan?.total || 0 }}</span> |
              <span>{{ row.options?.tongjiNum?.pinglun?.total || 0 }}</span> |
              <span>{{ row.options?.tongjiNum?.shoucan?.total || 0 }}</span> |
              <span>{{ row.enterWorkWaitSeconds || 6 }}</span> |
              <span>{{ row.afterOprWaitSeconds || 4 }}</span> |
              <span>{{ row.gender === 'all' ? '全部' : row.gender === 'male' ? '男' : '女' }}</span> |
              <span>{{ row.maxLikeCountForDianzan || 999 }}</span>
            </template>
          </el-table-column>

          <el-table-column label="代理ip" align="center">
            <template #default="{ row }">
              <span>{{ getProxyLabel(row.ips) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="运行时段" width="200" align="center">
            <template #default="{ row }">
              <div>
                <span>{{ formatRunHours(row.runHours, row.startTime, row.endTime) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="runMode" label="运行模式" width="80" align="center">
            <template #default="{ row }">
              <span>{{ row.runMode === 1 ? '火力' : '养号' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" align="center">
            <template #default="{ row }">
              <span :class="getStatusClass(row.status)">{{ row.status || '未打开' }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" label="操作" width="260" align="center">
            <template #default="{ row, $index }">
              <el-button
                size="small"
                :disabled="showCrackAlert || !isActivated"
                @click="openAccount(row)"
              >
                启动
              </el-button>
              
              <el-button
                size="small"
                :type="row.isPaused ? 'success' : 'danger'"
                :disabled="showCrackAlert || !isActivated"
                @click="togglePause(row)"
              >
                {{ row.isPaused ? '恢复' : '暂停' }}
              </el-button>
              
              <el-button
                size="small"
                :disabled="showCrackAlert || !isActivated"
                type="primary"
                @click="editAccount(row, $index)"
              >
                修改
              </el-button>
              
              <el-button
                size="small"
                :disabled="showCrackAlert || !isActivated"
                type="danger"
                @click="deleteAccount(row, $index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 批量配置区域 -->
      <div class="bottom-config-row">
        <el-row style="align-items: center">
          <div class="config-title">批量任务配置:</div>
          
          <span style="margin-right: 5px">关注</span>
          <el-input-number
            v-model="batchConfig.guanzhu"
            :min="0"
            :step="1"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
          
          <span style="margin-right: 5px">点赞</span>
          <el-input-number
            v-model="batchConfig.dianzan"
            :min="0"
            :step="1"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
          
          <span style="margin-right: 5px">评论</span>
          <el-input-number
            v-model="batchConfig.pinglun"
            :min="0"
            :step="1"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
          
          <span style="margin-right: 5px">收藏</span>
          <el-input-number
            v-model="batchConfig.shoucan"
            :min="0"
            :step="1"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
          
          <span style="margin-right: 5px">进作品等待(秒)</span>
          <el-input-number
            v-model="globalConfig.enterWorkWaitSeconds"
            :min="1"
            :max="60"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
          
          <span style="margin-right: 5px">操作等待(秒)</span>
          <el-input-number
            v-model="globalConfig.afterOprWaitSeconds"
            :min="1"
            :max="60"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
        </el-row>
        
        <el-row style="align-items: center; margin-top: 10px">
          <span style="margin-right: 5px">性别</span>
          <el-select
            v-model="globalConfig.gender"
            style="width: 90px; margin-right: 15px"
          >
            <el-option label="全部" value="all" />
            <el-option label="男" value="male" />
            <el-option label="女" value="female" />
          </el-select>
          
          <span style="margin-right: 5px">点赞次数少于</span>
          <el-input-number
            v-model="globalConfig.maxLikeCountForDianzan"
            :min="1"
            :max="9999"
            :controls="false"
            style="width: 90px; margin-right: 15px"
          />
          
          <span style="margin-right: 5px">才操作</span>
          
          <el-button
            type="primary"
            :disabled="selectedAccounts.length === 0"
            @click="applyBatchConfig"
          >
            应用配置
          </el-button>
          
          <span class="hint-text">1.选择账号 → 2.应用配置</span>
        </el-row>
      </div>
    </div>

    <!-- 新增账号对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="新增账号"
      width="550px"
      @close="resetAddForm"
    >
      <el-form>
        <el-form-item label="抖音id" required>
          <el-input
            v-model="newAccount.id"
            placeholder="输入你的抖音id"
          />
        </el-form-item>
        
        <el-form-item label="代理IP" required>
          <el-select
            v-model="newAccount.ips"
            placeholder="请选择代理IP"
            style="width: 200px"
            clearable
          >
            <el-option
              v-for="proxy in proxyList"
              :key="proxy.value"
              :label="proxy.label"
              :value="proxy.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="运行模式">
          <el-select
            v-model="newAccount.runMode"
            placeholder="请选择运行模式"
            style="width: 120px"
          >
            <el-option label="养号" :value="0" />
            <el-option label="火力" :value="1" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="运行时段" required>
          <div style="display: flex; flex-wrap: wrap; gap: 4px">
            <el-checkbox
              v-for="hour in 24"
              :key="hour - 1"
              v-model="newAccount.runHours"
              :label="hour - 1"
              style="width: 54px"
            >
              {{ formatHour(hour - 1) }}
            </el-checkbox>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="addAccount">确定</el-button>
      </template>
    </el-dialog>

    <!-- 修改账号对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="修改账号"
      width="550px"
      @close="resetEditForm"
    >
      <el-form>
        <el-form-item label="抖音id" required>
          <el-input
            v-model="editAccountData.id"
            placeholder="输入你的抖音id"
          />
        </el-form-item>

        <el-form-item label="代理IP" required>
          <el-select
            v-model="editAccountData.ips"
            placeholder="请选择代理IP"
            style="width: 200px"
            clearable
          >
            <el-option
              v-for="proxy in proxyList"
              :key="proxy.value"
              :label="proxy.label"
              :value="proxy.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="运行模式">
          <el-select
            v-model="editAccountData.runMode"
            placeholder="请选择运行模式"
            style="width: 120px"
          >
            <el-option label="养号" :value="0" />
            <el-option label="火力" :value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="运行时段" required>
          <div style="display: flex; flex-wrap: wrap; gap: 4px">
            <el-checkbox
              v-for="hour in 24"
              :key="hour - 1"
              v-model="editAccountData.runHours"
              :label="hour - 1"
              style="width: 54px"
            >
              {{ formatHour(hour - 1) }}
            </el-checkbox>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="updateAccount">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.app-container {
  padding: 20px;
  min-height: 100vh;
}

.tabs-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.kami-bar {
  display: flex;
  align-items: center;
}

.kami-info {
  color: #67c23a;
  font-size: 12px;
}

.wx-notify-setting-container {
  padding: 20px;
}

.ip-setting-container {
  padding: 20px;
}

.run-setting-container {
  padding: 20px;
}

.account-setting-container {
  padding: 20px;
}

.table-scroll-area {
  height: 400px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.bottom-config-row {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.config-title {
  font-weight: bold;
  margin-right: 15px;
}

.hint-text {
  color: #909399;
  font-size: 12px;
  margin-left: 15px;
}

.status-running {
  color: #67c23a;
}

.status-stopped {
  color: #909399;
}

.status-error {
  color: #f56c6c;
}

.status-paused {
  color: #e6a23c;
}
</style>

<script>
// 添加状态样式类方法
export default {
  methods: {
    getStatusClass(status) {
      switch (status) {
        case '运行中':
          return 'status-running'
        case '已停止':
        case '未打开':
          return 'status-stopped'
        case '错误':
        case '启动失败':
          return 'status-error'
        case '已暂停':
          return 'status-paused'
        default:
          return ''
      }
    }
  }
}
</script>
