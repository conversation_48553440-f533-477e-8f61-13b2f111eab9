<template>
  <div class="table-scroll-area">
    <el-table
      :data="accounts"
      style="width: 100%; height: calc(100vh - 150px); overflow: auto"
      :border="false"
      size="small"
    >
      <el-table-column label="窗口" type="index" width="50" />
      
      <el-table-column label="抖音号" align="left" width="120">
        <template #default="{ row }">
          <div>{{ row.options?.userName || '--' }}</div>
        </template>
      </el-table-column>
      
      <el-table-column label="近期" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="fans-def">{{ row.options?.fensiNum ?? '-' }}</span>
            <br>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="今日" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="f-red">{{ row.options?.jintianFans ?? '-' }}</span>
            <br>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="昨日" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="fans-def">{{ row.options?.zuotianFans ?? '-' }}</span>
            <br>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="关注" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="fans-num">{{ row.options?.tongjiNum?.guanzhu?.cur ?? '-' }}</span>
            <br>
            <span class="fans-label">{{ row.options?.tongjiNum?.guanzhu?.total ?? '-' }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="点赞" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="fans-num">{{ row.options?.tongjiNum?.dianzan?.cur ?? '-' }}</span>
            <br>
            <span class="fans-label">{{ row.options?.tongjiNum?.dianzan?.total ?? '-' }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="收藏" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="fans-num">{{ row.options?.tongjiNum?.shoucan?.cur ?? '-' }}</span>
            <br>
            <span class="fans-label">{{ row.options?.tongjiNum?.shoucan?.total ?? '-' }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="评论" align="center">
        <template #default="{ row }">
          <div class="cell-wrap">
            <span class="fans-num">{{ row.options?.tongjiNum?.pinglun?.cur ?? '-' }}</span>
            <br>
            <span class="fans-label">{{ row.options?.tongjiNum?.pinglun?.total ?? '-' }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="日志" align="center" width="240">
        <template #default="{ row }">
          {{ row.options?.status ?? '-' }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const accounts = ref([])
const tableHeight = ref(400)

// 调整表格高度
function adjustTableHeight() {
  const windowHeight = window.innerHeight
  const height = Math.max(220, windowHeight - 120)
  tableHeight.value = height
}

// 加载账号数据
const loadAccounts = async () => {
  try {
    // 修复：使用正确的IPC调用方式
    const accountList = await window.ipcRenderer.invoke('get-store', 'accountList')
    accounts.value = Array.isArray(accountList) ? accountList : []
  } catch (error) {
    console.error('加载账号数据失败', error)
    accounts.value = []
  }
}

// 监听账号行更新 - 与原始index.js逻辑保持一致
const handleAccountRowUpdated = (event, { id, newRow }) => {
  console.log('AccountTable收到账号行更新:', id, newRow)
  const index = accounts.value.findIndex(account => account.id === id)
  if (index !== -1) {
    // 完全按照原始index.js的更新逻辑
    accounts.value[index] = { ...accounts.value[index], ...newRow }
    console.log('AccountTable账号数据已更新:', accounts.value[index])
  }
}

let refreshInterval = null

onMounted(() => {
  loadAccounts()
  adjustTableHeight()

  // 监听窗口大小变化
  window.addEventListener('resize', adjustTableHeight)

  // 监听账号更新事件 - 按照原始index.js的逻辑
  if (window.ipcRenderer) {
    console.log('AccountTable: 注册account-row-updated事件监听')
    window.ipcRenderer.on('account-row-updated', handleAccountRowUpdated)
  }

  // 定时刷新账号数据 - 与原始index.js保持一致（1秒间隔）
  refreshInterval = setInterval(loadAccounts, 1 * 1000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }

  window.removeEventListener('resize', adjustTableHeight)

  // 正确移除IPC事件监听器
  if (window.ipcRenderer) {
    console.log('AccountTable: 移除account-row-updated事件监听')
    window.ipcRenderer.removeListener('account-row-updated', handleAccountRowUpdated)
  }
})
</script>

<style scoped>
.table-scroll-area {
  height: 100%;
}

.cell-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.2;
}

.fans-def {
  color: #606266;
  font-size: 12px;
}

.f-red {
  color: #f56c6c;
  font-size: 12px;
}

.fans-num {
  color: #409eff;
  font-weight: bold;
  font-size: 14px;
}

.fans-label {
  color: #909399;
  font-size: 11px;
}
</style>
