<template>
  <div class="ip-setting-container">
    <el-row :gutter="8" style="margin-bottom: 10px">
      <el-button type="primary" @click="showAddDialog">新增</el-button>
      <el-button type="primary" @click="showBatchAddDialog">批量新增</el-button>
    </el-row>

    <el-table
      :data="proxyList"
      border
      style="width: 100%; height: calc(100vh - 150px); overflow-y: auto"
    >
      <el-table-column prop="name" label="IP名称" />
      <el-table-column prop="ip" label="IP" />
      <el-table-column prop="port" label="端口" />
      <el-table-column prop="user" label="账号" />
      <el-table-column prop="pass" label="密码">
        <template #default="{ row }">
          <span>{{ row.pass ? '*****' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="protocol" label="协议类型" />
      <el-table-column label="操作" fixed="right">
        <template #default="{ row, $index }">
          <el-button
            @click="editProxy(row, $index)"
            size="small"
            type="primary"
            link
          >
            编辑
          </el-button>
          <el-button
            @click="deleteProxy(row, $index)"
            size="small"
            type="danger"
            link
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '新增IP' : '编辑IP'"
      width="400px"
    >
      <el-form :model="formData" label-width="80px">
        <el-form-item label="IP名称">
          <el-input
            v-model="formData.name"
            :disabled="dialogMode === 'edit'"
          />
        </el-form-item>
        <el-form-item label="IP">
          <el-input v-model="formData.ip" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口">
          <el-input v-model="formData.port" autocomplete="off" />
        </el-form-item>
        <el-form-item label="账号">
          <el-input v-model="formData.user" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input
            v-model="formData.pass"
            autocomplete="off"
            show-password
          />
        </el-form-item>
        <el-form-item label="协议类型">
          <el-select v-model="formData.protocol" placeholder="请选择">
            <el-option label="http" value="http" />
            <el-option label="socks5" value="socks5" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProxy">确定</el-button>
      </template>
    </el-dialog>

    <!-- 批量新增对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量新增"
      width="500px"
    >
      <div style="margin-bottom: 8px; color: #888; font-size: 13px">
        每行一个：ip|端口|账号|密码|http/socks5，账号/密码可留空。示例：<br>
        ***********|8080|user|pass|socks5
      </div>
      <el-input
        type="textarea"
        v-model="batchContent"
        :rows="8"
        placeholder="一行一个，格式：ip|端口|账号|密码|http/socks5"
      />
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="batchAddProxy">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, inject } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 注入应用管理器
const appManager = inject('appManager')

const proxyList = ref([])
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const dialogMode = ref('add')
const editIndex = ref(-1)
const batchContent = ref('')

const formData = reactive({
  name: '',
  ip: '',
  port: '',
  user: '',
  pass: '',
  protocol: 'http'
})

// 加载代理列表
async function loadProxyList() {
  try {
    await appManager.proxyManager.loadProxyList()
    proxyList.value = appManager.proxyManager.proxyList
  } catch (error) {
    console.error('加载代理列表失败', error)
    ElMessage.error('加载代理列表失败: ' + error.message)
  }
}

// 生成新的代理名称
function generateProxyName() {
  const numbers = proxyList.value
    .map(item => Number(item.name.replace('ip', '')))
    .filter(n => !isNaN(n))
  return `ip${(numbers.length ? Math.max(...numbers) : 0) + 1}`
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    name: generateProxyName(),
    ip: '',
    port: '',
    user: '',
    pass: '',
    protocol: 'http'
  })
}

// 显示新增对话框
function showAddDialog() {
  dialogMode.value = 'add'
  resetForm()
  editIndex.value = -1
  dialogVisible.value = true
}

// 编辑代理
function editProxy(row, index) {
  dialogMode.value = 'edit'
  Object.assign(formData, row)
  editIndex.value = index
  dialogVisible.value = true
}

// 保存代理
async function saveProxy() {
  try {
    if (dialogMode.value === 'add') {
      await appManager.proxyManager.addProxy(formData)
      ElMessage.success('代理添加成功')
    } else if (dialogMode.value === 'edit') {
      await appManager.proxyManager.updateProxy(editIndex.value, formData)
      ElMessage.success('代理更新成功')
    }
    
    // 刷新列表
    proxyList.value = appManager.proxyManager.proxyList
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error(error.message)
  }
}

// 删除代理
async function deleteProxy(row, index) {
  try {
    await ElMessageBox.confirm('确定要删除该代理IP吗？', '提示', {
      type: 'warning'
    })
    
    await appManager.proxyManager.deleteProxy(index)
    proxyList.value = appManager.proxyManager.proxyList
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 显示批量新增对话框
function showBatchAddDialog() {
  batchContent.value = ''
  batchDialogVisible.value = true
}

// 批量新增代理
async function batchAddProxy() {
  if (!batchContent.value.trim()) {
    ElMessage.error('内容不能为空')
    return
  }

  try {
    const lines = batchContent.value.split('\n')
      .map(line => line.trim())
      .filter(Boolean)
    
    await appManager.proxyManager.batchAddProxies(lines)
    proxyList.value = appManager.proxyManager.proxyList
    batchDialogVisible.value = false
    ElMessage.success('批量添加成功')
  } catch (error) {
    ElMessage.error('批量添加失败: ' + error.message)
  }
}

onMounted(async () => {
  await loadProxyList()
})
</script>

<style scoped>
.ip-setting-container {
  padding: 20px;
}
</style>