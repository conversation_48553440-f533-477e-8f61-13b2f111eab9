// 主入口文件 - 整合所有业务逻辑
import { createApp } from 'vue'
import App from './App.vue'
import { appManager } from './utils/app-manager.js'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason)
})

// 创建Vue应用
const app = createApp(App)

// 使用Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局属性
app.config.globalProperties.$appManager = appManager

// 全局方法
app.provide('appManager', appManager)

// 应用初始化
async function initializeApp() {
  try {
    console.log('开始初始化应用...')
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve)
      })
    }
    
    // 初始化应用管理器
    const initResult = await appManager.initialize()
    console.log('应用初始化结果:', initResult)
    
    // 挂载Vue应用
    app.mount('#app')
    
    console.log('Vue应用挂载完成')
    
    // 如果卡密无效，显示激活界面
    if (!initResult.licenseInfo.isActive) {
      console.log('卡密未激活，需要用户激活')
    }
    
  } catch (error) {
    console.error('应用初始化失败:', error)
    
    // 显示错误信息
    document.body.innerHTML = `
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: Arial, sans-serif;
        background: #f5f5f5;
      ">
        <div style="
          background: white;
          padding: 40px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          text-align: center;
          max-width: 400px;
        ">
          <h2 style="color: #e74c3c; margin-bottom: 20px;">应用初始化失败</h2>
          <p style="color: #666; margin-bottom: 20px;">${error.message}</p>
          <button onclick="location.reload()" style="
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
          ">重新加载</button>
        </div>
      </div>
    `
  }
}

// 应用清理
function cleanupApp() {
  console.log('应用即将关闭，开始清理...')
  appManager.cleanup()
}

// 监听页面卸载事件
window.addEventListener('beforeunload', cleanupApp)

// 启动应用
initializeApp()

// 导出给其他模块使用
export { appManager }