// 账号管理相关功能
const ACCOUNT_LIST_KEY = 'accountList'

export class AccountManager {
  constructor() {
    this.accounts = []
    this.proxyList = []
  }

  // 创建默认账号选项
  createDefaultOptions() {
    return {
      fensiNum: 0,
      jintianFans: 0,
      zuotianFans: 0,
      todayStartFans: 0,
      guanzhuNum: 0,
      userName: '',
      lastResetDate: '',
      todayTarget: [],
      isPaused: false,
      lastOprateTime: '',
      tongjiNum: {
        dianzan: { cur: 0, total: 1500 },
        tuwen: { cur: 0, total: 0 },
        shoucan: { cur: 0, total: 150 },
        pinglun: { cur: 0, total: 180 },
        fenxiang: { cur: 0, total: 0 },
        sixin: { cur: 0, total: 0 },
        guanzhu: { cur: 0, total: 10 }
      }
    }
  }

  // 加载账号列表
  async loadAccounts() {
    try {
      const data = await window.ipcRenderer.invoke('get-store', ACCOUNT_LIST_KEY) || []
      this.accounts = data.map((account, index) => ({
        ...account,
        status: '未打开',
        isPaused: false,
        isPopup: false,
        bandDianzanNum: 0,
        bandShoucanNum: 0,
        bandPinglunNum: 0,
        startTime: account.startTime || '08:00',
        endTime: account.endTime || '24:00',
        runMode: typeof account.runMode === 'number' ? account.runMode : 0,
        enterWorkWaitSeconds: account.enterWorkWaitSeconds ?? 6,
        afterOprWaitSeconds: account.afterOprWaitSeconds ?? 4,
        gender: account.gender ?? 'all',
        maxLikeCountForDianzan: account.maxLikeCountForDianzan ?? 999
      }))
      return this.accounts
    } catch (error) {
      console.error('加载账号数据失败', error)
      this.accounts = []
      return []
    }
  }

  // 保存账号列表
  async saveAccounts() {
    try {
      await window.ipcRenderer.invoke('set-store', ACCOUNT_LIST_KEY, JSON.parse(JSON.stringify(this.accounts)))
    } catch (error) {
      console.error('保存账号数据失败', error)
    }
  }

  // 添加账号
  async addAccount(accountData) {
    const { id, ips, startTime, endTime, runMode, runHours, enterWorkWaitSeconds, afterOprWaitSeconds, gender, maxLikeCountForDianzan } = accountData
    
    if (!id || !id.trim()) {
      throw new Error('请输入抖音id')
    }

    const proxyInfo = this.proxyList.find(proxy => proxy.value === ips)
    if (!proxyInfo) {
      throw new Error('请选择代理IP')
    }

    const index = this.accounts.length ? Math.max(...this.accounts.map(acc => acc.index)) + 1 : 1

    const newAccount = {
      index,
      id: id.trim(),
      protocol: proxyInfo.protocol ?? '',
      ips,
      status: '未打开',
      isPaused: false,
      isPopup: false,
      bandDianzanNum: 0,
      bandShoucanNum: 0,
      bandPinglunNum: 0,
      options: this.createDefaultOptions(),
      startTime,
      endTime,
      runMode,
      runHours: [...runHours],
      enterWorkWaitSeconds,
      afterOprWaitSeconds,
      gender,
      maxLikeCountForDianzan
    }

    this.accounts.push(newAccount)
    await this.saveAccounts()
    return newAccount
  }

  // 更新账号
  async updateAccount(index, accountData) {
    if (index < 0 || index >= this.accounts.length) {
      throw new Error('数据异常')
    }

    const { id, ips, startTime, endTime, runMode, runHours } = accountData
    
    if (!id || !id.trim()) {
      throw new Error('请输入抖音id')
    }

    const proxyInfo = this.proxyList.find(proxy => proxy.value === ips)
    if (!proxyInfo) {
      throw new Error('请选择代理IP')
    }

    const account = this.accounts[index]
    account.id = id.trim()
    account.ips = ips
    account.protocol = proxyInfo.protocol ?? ''
    account.startTime = startTime
    account.endTime = endTime
    account.runMode = runMode
    account.runHours = [...runHours]

    await this.saveAccounts()
    return account
  }

  // 删除账号
  async deleteAccount(index) {
    if (index < 0 || index >= this.accounts.length) {
      throw new Error('数据异常')
    }

    const account = this.accounts[index]
    this.accounts.splice(index, 1)
    
    // 重新分配索引
    this.accounts.forEach((acc, idx) => {
      acc.index = idx + 1
    })

    await this.saveAccounts()
    
    // 通知主进程删除账号
    if (window.ipcRenderer) {
      window.ipcRenderer.send('deleteAccount', account.id)
    }

    return account
  }

  // 批量配置账号
  async batchConfigAccounts(selectedAccounts, config) {
    const { taskConfig, enterWorkWaitSeconds, afterOprWaitSeconds, gender, maxLikeCountForDianzan } = config
    const taskTypes = ['guanzhu', 'dianzan', 'pinglun', 'shoucan']

    selectedAccounts.forEach(account => {
      // 更新任务配置
      taskTypes.forEach(taskType => {
        account.options.tongjiNum[taskType].total = taskConfig[taskType]
        account.options.tongjiNum[taskType].cur = 0
      })

      // 更新其他配置
      account.enterWorkWaitSeconds = enterWorkWaitSeconds
      account.afterOprWaitSeconds = afterOprWaitSeconds
      account.gender = gender
      account.maxLikeCountForDianzan = maxLikeCountForDianzan

      // 通知主进程更新账号行
      if (window.ipcRenderer) {
        window.ipcRenderer.invoke('update-account-row', {
          id: account.id,
          newRow: {
            options: JSON.parse(JSON.stringify(account.options))
          }
        })
      }
    })

    await this.saveAccounts()
  }

  // 检查是否在允许运行时间段
  isInAllowedTimeRange(startTime, endTime, currentTime = new Date()) {
    const [startHour, startMinute] = startTime.split(':').map(Number)
    let [endHour, endMinute] = endTime.split(':').map(Number)
    
    if (endHour === 24) endHour = 0

    const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes()
    const startMinutes = startHour * 60 + startMinute
    const endMinutes = endHour * 60 + endMinute

    if (startMinutes < endMinutes) {
      return currentMinutes >= startMinutes && currentMinutes < endMinutes
    } else {
      return currentMinutes >= startMinutes || currentMinutes < endMinutes
    }
  }

  // 格式化运行时段显示
  formatRunHours(runHours) {
    if (!runHours?.length) return ''
    
    const sortedHours = [...runHours].sort((a, b) => a - b)
    const ranges = []
    let start = sortedHours[0]
    let end = sortedHours[0]

    for (let i = 1; i <= sortedHours.length; i++) {
      if (sortedHours[i] === end + 1) {
        end = sortedHours[i]
      } else {
        ranges.push(start === end ? 
          `${start.toString().padStart(2, '0')}:00` : 
          `${start.toString().padStart(2, '0')}:00-${(end + 1).toString().padStart(2, '0')}:00`
        )
        start = sortedHours[i]
        end = sortedHours[i]
      }
    }

    return ranges.join(', ')
  }

  // 设置代理列表
  setProxyList(proxyList) {
    this.proxyList = proxyList
  }
}