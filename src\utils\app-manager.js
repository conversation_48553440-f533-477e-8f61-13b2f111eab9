// 应用主业务逻辑管理器
import { LicenseManager } from './license.js'
import { AccountManager } from './account.js'
import { ProxyManager } from './proxy.js'
import { WechatNotifier } from './wechat.js'
import { AutomationController } from './automation.js'
import { StatisticsManager } from './statistics.js'
import { ConfigManager } from './config.js'

export class AppManager {
  constructor() {
    // 初始化各个管理器
    this.licenseManager = new LicenseManager()
    this.accountManager = new AccountManager()
    this.proxyManager = new ProxyManager()
    this.wechatNotifier = new WechatNotifier()
    this.automationController = new AutomationController()
    this.statisticsManager = new StatisticsManager()
    this.configManager = new ConfigManager()
    
    // 应用状态
    this.isInitialized = false
    this.isLicenseValid = false
    
    // 绑定事件处理
    this.bindEvents()
  }

  // 初始化应用
  async initialize() {
    try {
      console.log('开始初始化应用...')
      
      // 1. 加载配置
      await this.configManager.loadConfig()
      console.log('配置加载完成')
      
      // 2. 初始化卡密管理
      const licenseInfo = await this.licenseManager.initialize()
      this.isLicenseValid = licenseInfo.isActive
      console.log('卡密验证状态:', this.isLicenseValid)
      
      // 3. 加载代理IP列表
      await this.proxyManager.loadProxyList()
      console.log('代理IP列表加载完成')
      
      // 4. 设置账号管理器的代理列表
      this.accountManager.setProxyList(this.proxyManager.getFormattedProxyList())
      
      // 5. 加载账号列表
      await this.accountManager.loadAccounts()
      console.log('账号列表加载完成')
      
      // 6. 加载微信配置
      await this.wechatNotifier.loadConfig()
      console.log('微信配置加载完成')
      
      // 7. 加载统计数据
      await this.statisticsManager.loadStats()
      console.log('统计数据加载完成')
      
      // 8. 更新统计中的总账号数
      this.statisticsManager.updateTotalAccounts(this.accountManager.accounts.length)
      
      this.isInitialized = true
      console.log('应用初始化完成')
      
      return {
        success: true,
        licenseInfo,
        accountCount: this.accountManager.accounts.length,
        proxyCount: this.proxyManager.proxyList.length
      }
    } catch (error) {
      console.error('应用初始化失败:', error)
      throw error
    }
  }

  // 绑定事件处理
  bindEvents() {
    if (window.ipcRenderer) {
      // 监听主进程的账号状态更新
      window.ipcRenderer.on('account-status-update', (event, data) => {
        this.handleAccountStatusUpdate(data)
      })
      
      // 监听主进程的统计数据更新
      window.ipcRenderer.on('stats-update', (event, data) => {
        this.handleStatsUpdate(data)
      })
      
      // 监听主进程的错误信息
      window.ipcRenderer.on('account-error', (event, data) => {
        this.handleAccountError(data)
      })
      
      // 监听窗口关闭事件
      window.ipcRenderer.on('app-will-quit', () => {
        this.cleanup()
      })
    }
  }

  // 处理账号状态更新
  handleAccountStatusUpdate(data) {
    const { accountId, status, details } = data
    const account = this.accountManager.accounts.find(acc => acc.id === accountId)
    
    if (account) {
      account.status = status
      if (details) {
        // 更新账号详细信息
        Object.assign(account.options, details)
      }
      
      // 更新统计数据
      if (details && details.tongjiNum) {
        this.statisticsManager.updateAccountStats(accountId, details.tongjiNum)
      }
      
      // 发送微信通知
      if (this.wechatNotifier.config.isEnabled) {
        this.wechatNotifier.sendAccountStatus(accountId, status, details?.message)
      }
    }
  }

  // 处理统计数据更新
  handleStatsUpdate(data) {
    const { accountId, stats } = data
    this.statisticsManager.updateAccountStats(accountId, stats)
    this.automationController.updateAccountStats(accountId, stats)
  }

  // 处理账号错误
  handleAccountError(data) {
    const { accountId, error } = data
    const account = this.accountManager.accounts.find(acc => acc.id === accountId)
    
    if (account) {
      account.status = '错误'
      
      // 发送微信错误通知
      if (this.wechatNotifier.config.isEnabled) {
        this.wechatNotifier.sendError(accountId, error)
      }
    }
  }

  // 激活卡密
  async activateLicense(licenseKey) {
    try {
      const result = await this.licenseManager.activateLicense(licenseKey)
      this.isLicenseValid = true
      return result
    } catch (error) {
      this.isLicenseValid = false
      throw error
    }
  }

  // 启动账号
  async startAccount(accountIndex) {
    if (!this.isLicenseValid) {
      throw new Error('请先激活卡密')
    }
    
    const account = this.accountManager.accounts[accountIndex]
    if (!account) {
      throw new Error('账号不存在')
    }
    
    return await this.automationController.startAccount(account)
  }

  // 停止账号
  async stopAccount(accountIndex) {
    const account = this.accountManager.accounts[accountIndex]
    if (!account) {
      throw new Error('账号不存在')
    }
    
    return await this.automationController.stopAccount(account)
  }

  // 批量启动账号
  async batchStartAccounts(accountIndexes) {
    if (!this.isLicenseValid) {
      throw new Error('请先激活卡密')
    }
    
    const accounts = accountIndexes.map(index => this.accountManager.accounts[index]).filter(Boolean)
    return await this.automationController.batchStartAccounts(accounts)
  }

  // 批量停止账号
  async batchStopAccounts(accountIndexes) {
    const accounts = accountIndexes.map(index => this.accountManager.accounts[index]).filter(Boolean)
    return await this.automationController.batchStopAccounts(accounts)
  }

  // 全局暂停/恢复
  async toggleGlobalPause() {
    return await this.automationController.toggleGlobalPause()
  }

  // 添加账号
  async addAccount(accountData) {
    const result = await this.accountManager.addAccount(accountData)
    this.statisticsManager.updateTotalAccounts(this.accountManager.accounts.length)
    return result
  }

  // 更新账号
  async updateAccount(index, accountData) {
    return await this.accountManager.updateAccount(index, accountData)
  }

  // 删除账号
  async deleteAccount(index) {
    const result = await this.accountManager.deleteAccount(index)
    this.statisticsManager.updateTotalAccounts(this.accountManager.accounts.length)
    return result
  }

  // 批量配置账号
  async batchConfigAccounts(selectedIndexes, config) {
    const selectedAccounts = selectedIndexes.map(index => this.accountManager.accounts[index]).filter(Boolean)
    return await this.accountManager.batchConfigAccounts(selectedAccounts, config)
  }

  // 添加代理IP
  async addProxy(proxyConfig) {
    const result = await this.proxyManager.addProxy(proxyConfig)
    // 更新账号管理器的代理列表
    this.accountManager.setProxyList(this.proxyManager.getFormattedProxyList())
    return result
  }

  // 更新代理IP
  async updateProxy(index, proxyConfig) {
    const result = await this.proxyManager.updateProxy(index, proxyConfig)
    this.accountManager.setProxyList(this.proxyManager.getFormattedProxyList())
    return result
  }

  // 删除代理IP
  async deleteProxy(index) {
    const result = await this.proxyManager.deleteProxy(index)
    this.accountManager.setProxyList(this.proxyManager.getFormattedProxyList())
    return result
  }

  // 批量添加代理IP
  async batchAddProxies(batchContent) {
    const result = await this.proxyManager.batchAddProxies(batchContent)
    this.accountManager.setProxyList(this.proxyManager.getFormattedProxyList())
    return result
  }

  // 测试微信通知
  async testWechatNotification() {
    return await this.wechatNotifier.testConnection()
  }

  // 保存微信配置
  async saveWechatConfig(config) {
    return await this.wechatNotifier.saveConfig(config)
  }

  // 发送每日统计
  async sendDailyStats() {
    const stats = this.statisticsManager.getTodayStats()
    const efficiency = this.statisticsManager.calculateEfficiency()
    
    const statsData = {
      totalAccounts: stats.totalAccounts,
      activeAccounts: stats.activeAccounts,
      totalLikes: stats.totalLikes,
      totalFollows: stats.totalFollows,
      totalComments: stats.totalComments,
      totalCollects: stats.totalCollects,
      ...efficiency
    }
    
    return await this.wechatNotifier.sendDailyStats(statsData)
  }

  // 获取应用状态
  getAppStatus() {
    return {
      isInitialized: this.isInitialized,
      isLicenseValid: this.isLicenseValid,
      licenseExpireTime: this.licenseManager.expireTime,
      deviceId: this.licenseManager.deviceId,
      runningStats: this.automationController.getRunningStats(),
      todayStats: this.statisticsManager.getTodayStats(),
      efficiency: this.statisticsManager.calculateEfficiency(),
      accountCount: this.accountManager.accounts.length,
      proxyCount: this.proxyManager.proxyList.length,
      wechatEnabled: this.wechatNotifier.config.isEnabled
    }
  }

  // 获取图表数据
  getChartData(days = 7) {
    return this.statisticsManager.getChartData(days)
  }

  // 导出数据
  exportData(type = 'all') {
    const exportData = {}
    
    if (type === 'all' || type === 'accounts') {
      exportData.accounts = this.accountManager.accounts
    }
    
    if (type === 'all' || type === 'proxies') {
      exportData.proxies = this.proxyManager.proxyList
    }
    
    if (type === 'all' || type === 'stats') {
      exportData.stats = this.statisticsManager.exportStats()
    }
    
    if (type === 'all' || type === 'config') {
      exportData.config = this.configManager.exportConfig()
    }
    
    return {
      data: exportData,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }
  }

  // 导入数据
  async importData(importData) {
    try {
      const { data, version } = importData
      
      if (data.accounts) {
        this.accountManager.accounts = data.accounts
        await this.accountManager.saveAccounts()
      }
      
      if (data.proxies) {
        this.proxyManager.proxyList = data.proxies
        await this.proxyManager.saveProxyList()
        this.accountManager.setProxyList(this.proxyManager.getFormattedProxyList())
      }
      
      if (data.config) {
        await this.configManager.importConfig(data.config)
      }
      
      return { success: true, message: '数据导入成功' }
    } catch (error) {
      return { success: false, message: `数据导入失败: ${error.message}` }
    }
  }

  // 重置应用数据
  async resetAppData() {
    try {
      // 停止所有运行中的账号
      const runningAccounts = Array.from(this.automationController.runningAccounts.values())
      for (const accountInfo of runningAccounts) {
        await this.automationController.stopAccount(accountInfo.account)
      }
      
      // 重置各个管理器
      this.accountManager.accounts = []
      await this.accountManager.saveAccounts()
      
      this.proxyManager.proxyList = []
      await this.proxyManager.saveProxyList()
      
      await this.statisticsManager.resetTodayStats()
      await this.configManager.resetConfig()
      
      // 重新初始化
      await this.initialize()
      
      return { success: true, message: '应用数据重置成功' }
    } catch (error) {
      return { success: false, message: `重置失败: ${error.message}` }
    }
  }

  // 清理资源
  cleanup() {
    console.log('开始清理应用资源...')
    
    // 停止卡密心跳检查
    this.licenseManager.stopHeartbeat()
    
    // 清理自动化控制器
    this.automationController.cleanup()
    
    // 保存最终数据
    this.accountManager.saveAccounts().catch(console.error)
    this.proxyManager.saveProxyList().catch(console.error)
    this.statisticsManager.saveStats().catch(console.error)
    this.configManager.saveConfig().catch(console.error)
    
    console.log('应用资源清理完成')
  }

  // 获取版本信息
  getVersionInfo() {
    return {
      version: '1.0.0',
      buildTime: '2024-01-01',
      author: 'WuStart Team',
      description: '抖音自动化工具'
    }
  }
}

// 创建全局应用管理器实例
export const appManager = new AppManager()

// 导出给Vue组件使用的方法
export const useAppManager = () => {
  return {
    appManager,
    
    // 便捷方法
    async init() {
      return await appManager.initialize()
    },
    
    getStatus() {
      return appManager.getAppStatus()
    },
    
    async activateLicense(key) {
      return await appManager.activateLicense(key)
    },
    
    async startAccount(index) {
      return await appManager.startAccount(index)
    },
    
    async stopAccount(index) {
      return await appManager.stopAccount(index)
    },
    
    getAccounts() {
      return appManager.accountManager.accounts
    },
    
    getProxies() {
      return appManager.proxyManager.proxyList
    },
    
    getStats() {
      return appManager.statisticsManager.getTodayStats()
    },
    
    getChartData(days) {
      return appManager.getChartData(days)
    }
  }
}