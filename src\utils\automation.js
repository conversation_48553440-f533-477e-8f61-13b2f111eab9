// 自动化任务控制相关功能
export class AutomationController {
  constructor() {
    this.runningAccounts = new Map() // 存储正在运行的账号
    this.taskTimers = new Map() // 存储定时器
    this.globalSettings = {
      isGlobalPaused: false,
      maxConcurrentAccounts: 5
    }
  }

  // 启动账号任务（从 index.js 提取的 se(re) 函数逻辑）
  async startAccount(account) {
    if (this.runningAccounts.has(account.id)) {
      throw new Error('账号已在运行中')
    }

    // 时间段校验（index.js 的 $e(he, Ne) 函数逻辑）
    const startTime = account.startTime || "08:00"
    const endTime = account.endTime || "24:00"
    if (!this.isInAllowedTime(startTime, endTime)) {
      throw new Error('当前不在允许运行时间段，无法操作！')
    }

    try {
      // index.js 的 se(re) 核心逻辑：重置状态并克隆发送
      account.status = "未打开"
      account.isPaused = false
      account.isPopup = false
      account.bandDianzanNum = 0
      account.bandShoucanNum = 0
      account.bandPinglunNum = 0

      // 克隆账号数据并发送给主进程（与 index.js 完全一致）
      const clonedRow = JSON.parse(JSON.stringify(account))
      console.log("clonedRow", clonedRow)
      window.ipcRenderer?.send("open-google-window", clonedRow)

      // 本地状态管理
      account.status = '运行中'
      this.runningAccounts.set(account.id, {
        account,
        startTime: new Date(),
        lastActivity: new Date()
      })

      // 启动监控定时器
      this.startAccountMonitor(account)

      return { success: true, message: '启动成功' }
    } catch (error) {
      account.status = '启动失败'
      throw error
    }
  }

  // 暂停/恢复账号任务（从 index.js 提取的 ce(re) 函数逻辑）
  async toggleAccountPause(account) {
    const startTime = account.startTime || "08:00"
    const endTime = account.endTime || "24:00"

    // 时间段校验（与 index.js 的 ce(re) 一致）
    if (!this.isInAllowedTime(startTime, endTime)) {
      throw new Error("当前不在允许运行时间段，无法操作！")
    }

    try {
      const clonedRow = JSON.parse(JSON.stringify(account))

      if (account.isPaused) {
        // 恢复运行
        window.ipcRenderer?.send("resume-google-window", clonedRow)
        account.isPaused = false
        account.status = '运行中'
        return { success: true, message: '恢复成功' }
      } else {
        // 暂停运行
        window.ipcRenderer?.send("stop-google-window", clonedRow)
        account.isPaused = true
        account.status = '已暂停'
        return { success: true, message: '暂停成功' }
      }
    } catch (error) {
      throw error
    }
  }

  // 停止账号任务（完全停止并清理）
  async stopAccount(account) {
    try {
      const clonedRow = JSON.parse(JSON.stringify(account))
      window.ipcRenderer?.send("stop-google-window", clonedRow)

      // 清理本地状态
      this.runningAccounts.delete(account.id)
      this.clearAccountTimer(account.id)

      account.status = '已停止'
      account.isPaused = false

      return { success: true, message: '停止成功' }
    } catch (error) {
      account.status = '停止失败'
      throw error
    }
  }

  // 删除账号（从 index.js 提取的删除账号逻辑）
  async deleteAccount(account) {
    try {
      // 发送删除账号事件到主进程
      window.ipcRenderer?.send("deleteAccount", account.id)

      // 清理本地状态
      this.runningAccounts.delete(account.id)
      this.taskTimers.delete(account.id)

      return { success: true, message: '删除成功' }
    } catch (error) {
      throw error
    }
  }

  // 兼容旧接口
  async pauseAccount(account) {
    return await this.toggleAccountPause(account)
  }

  async resumeAccount(account) {
    return await this.toggleAccountPause(account)
  }

  // 批量启动账号
  async batchStartAccounts(accounts) {
    const results = []
    
    for (const account of accounts) {
      try {
        const result = await this.startAccount(account)
        results.push({ account: account.id, ...result })
        
        // 启动间隔，避免同时启动过多账号
        await this.sleep(2000)
      } catch (error) {
        results.push({ 
          account: account.id, 
          success: false, 
          message: error.message 
        })
      }
    }
    
    return results
  }

  // 批量停止账号
  async batchStopAccounts(accounts) {
    const results = []
    
    for (const account of accounts) {
      try {
        const result = await this.stopAccount(account)
        results.push({ account: account.id, ...result })
      } catch (error) {
        results.push({ 
          account: account.id, 
          success: false, 
          message: error.message 
        })
      }
    }
    
    return results
  }

  // 全局暂停/恢复
  async toggleGlobalPause() {
    this.globalSettings.isGlobalPaused = !this.globalSettings.isGlobalPaused
    
    if (this.globalSettings.isGlobalPaused) {
      // 暂停所有运行中的账号
      for (const [accountId, accountInfo] of this.runningAccounts) {
        await this.pauseAccount(accountInfo.account)
      }
    } else {
      // 恢复所有暂停的账号
      for (const [accountId, accountInfo] of this.runningAccounts) {
        if (accountInfo.account.isPaused) {
          await this.resumeAccount(accountInfo.account)
        }
      }
    }
    
    return this.globalSettings.isGlobalPaused
  }

  // 时间段校验（从 index.js 提取的 $e(re, Y, he, Ne) 函数逻辑）
  isInAllowedTime(startTime, endTime, currentTime = new Date(), runHours = null) {
    try {
      const [startHour, startMinute] = startTime.split(":").map(Number)
      let [endHour, endMinute] = endTime.split(":").map(Number)

      // 处理 24:00 的情况
      if (endHour === 24) endHour = 0

      const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes()
      const startMinutes = startHour * 60 + startMinute
      const endMinutes = endHour * 60 + endMinute

      if (startMinutes < endMinutes) {
        // 同一天内的时间段
        return currentMinutes >= startMinutes && currentMinutes < endMinutes
      } else {
        // 跨天的时间段
        return currentMinutes >= startMinutes || currentMinutes < endMinutes
      }
    } catch (error) {
      console.error('时间段校验失败:', error)
      return true // 默认允许运行
    }
  }

  // 账号运行模式校验（支持 runMode 和 runHours）
  isAccountInAllowedTime(account) {
    const now = new Date()
    const currentHour = now.getHours()

    // 检查运行模式
    if (account.runMode === 1 && account.runHours?.length) {
      // 指定时段模式（火力模式）
      return account.runHours.includes(currentHour)
    } else {
      // 时间段模式（养号模式）
      const startTime = account.startTime || "08:00"
      const endTime = account.endTime || "24:00"
      return this.isInAllowedTime(startTime, endTime, now)
    }
  }

  // 启动账号监控
  startAccountMonitor(account) {
    const timerId = setInterval(() => {
      this.checkAccountStatus(account)
    }, 30000) // 每30秒检查一次
    
    this.taskTimers.set(account.id, timerId)
  }

  // 检查账号状态（基于 index.js 的运行逻辑）
  async checkAccountStatus(account) {
    try {
      // 检查是否还在允许的运行时间内
      if (!this.isAccountInAllowedTime(account)) {
        console.log(`账号 ${account.id} 超出运行时间，自动停止`)
        await this.stopAccount(account)
        return
      }

      // 更新最后活动时间
      const accountInfo = this.runningAccounts.get(account.id)
      if (accountInfo) {
        accountInfo.lastActivity = new Date()
      }

      // 检查任务完成情况
      await this.checkTaskCompletion(account)
    } catch (error) {
      console.error(`检查账号 ${account.id} 状态失败:`, error)
    }
  }

  // 检查任务完成情况
  async checkTaskCompletion(account) {
    const { tongjiNum } = account.options
    const taskTypes = ['dianzan', 'guanzhu', 'pinglun', 'shoucan']
    
    let allCompleted = true
    
    for (const taskType of taskTypes) {
      const task = tongjiNum[taskType]
      if (task.total > 0 && task.cur < task.total) {
        allCompleted = false
        break
      }
    }
    
    if (allCompleted) {
      console.log(`账号 ${account.id} 所有任务已完成，自动停止`)
      await this.stopAccount(account)
    }
  }

  // 清理账号定时器
  clearAccountTimer(accountId) {
    const timerId = this.taskTimers.get(accountId)
    if (timerId) {
      clearInterval(timerId)
      this.taskTimers.delete(accountId)
    }
  }

  // 获取运行统计
  getRunningStats() {
    return {
      runningCount: this.runningAccounts.size,
      pausedCount: Array.from(this.runningAccounts.values())
        .filter(info => info.account.isPaused).length,
      totalRunTime: Array.from(this.runningAccounts.values())
        .reduce((total, info) => {
          return total + (new Date() - info.startTime)
        }, 0),
      isGlobalPaused: this.globalSettings.isGlobalPaused
    }
  }

  // 设置核对 IP 开关（从 index.js 提取的 set-only-baidu-proxy 逻辑）
  setOnlyBaiduProxy(enabled) {
    try {
      window.ipcRenderer?.send('set-only-baidu-proxy', !!enabled)
    } catch (error) {
      console.error('设置核对IP开关失败:', error)
    }
  }

  // 批量打开核对窗口（从 index.js 提取的勾选行为）
  openBaiduVerifyForTargets(rows) {
    if (!Array.isArray(rows) || rows.length === 0) return 0
    let opened = 0
    for (const row of rows) {
      if (row?.ips && row.ips !== '未代理') {
        try {
          const clonedRow = JSON.parse(JSON.stringify(row))
          window.ipcRenderer?.send('open-google-window', clonedRow)
          opened++
        } catch (error) {
          console.error(`打开核对窗口失败 ${row.id}:`, error)
        }
      }
    }
    return opened
  }

  // 初始化运行详情监听（从 index.js 提取的 ge() 函数逻辑）
  initRunDetailsListener() {
    try {
      // 设置默认核对IP状态为false（与 index.js 的 ge() 函数一致）
      window.ipcRenderer?.send("set-only-baidu-proxy", false)

      // 监听账号行更新事件（与 index.js 的监听逻辑一致）
      window.ipcRenderer?.on("account-row-updated", (event, { id, newRow }) => {
        console.log('账号行更新:', id, newRow)
        // 触发UI更新回调
        if (this.onAccountRowUpdated) {
          this.onAccountRowUpdated(id, newRow)
        }
      })

      // 监听统计更新事件（用于运行详情实时更新）
      window.ipcRenderer?.on("stats-update", (event, { accountId, stats }) => {
        console.log('统计更新:', accountId, stats)
        if (this.onStatsUpdated) {
          this.onStatsUpdated(accountId, stats)
        }
      })

      // 监听账号状态更新事件
      window.ipcRenderer?.on("account-status-update", (event, { accountId, status }) => {
        console.log('状态更新:', accountId, status)
        if (this.onStatusUpdated) {
          this.onStatusUpdated(accountId, status)
        }
      })

      console.log('运行详情监听初始化完成')
    } catch (error) {
      console.error('初始化运行详情监听失败:', error)
    }
  }

  // 设置各种更新回调
  setAccountRowUpdatedCallback(callback) {
    this.onAccountRowUpdated = callback
  }

  setStatsUpdatedCallback(callback) {
    this.onStatsUpdated = callback
  }

  setStatusUpdatedCallback(callback) {
    this.onStatusUpdated = callback
  }

  // 清理所有任务
  cleanup() {
    // 清理所有定时器
    for (const timerId of this.taskTimers.values()) {
      clearInterval(timerId)
    }
    this.taskTimers.clear()

    // 清理运行状态
    this.runningAccounts.clear()

    // 移除事件监听
    try {
      window.ipcRenderer?.removeAllListeners("account-row-updated")
      window.ipcRenderer?.removeAllListeners("stats-update")
      window.ipcRenderer?.removeAllListeners("account-status-update")
    } catch (error) {
      console.error('清理事件监听失败:', error)
    }
  }

  // 工具方法：延时
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 更新账号统计数据
  updateAccountStats(accountId, stats) {
    const accountInfo = this.runningAccounts.get(accountId)
    if (accountInfo) {
      const { account } = accountInfo
      const { tongjiNum } = account.options
      
      // 更新各项统计
      Object.keys(stats).forEach(key => {
        if (tongjiNum[key]) {
          tongjiNum[key].cur = stats[key]
        }
      })
      
      // 更新粉丝数据
      if (stats.fensiNum !== undefined) {
        account.options.fensiNum = stats.fensiNum
        account.options.jintianFans = stats.jintianFans || 0
      }
      
      // 更新最后操作时间
      account.options.lastOprateTime = new Date().toLocaleString()
    }
  }

  // 设置最大并发数
  setMaxConcurrentAccounts(max) {
    this.globalSettings.maxConcurrentAccounts = Math.max(1, Math.min(20, max))
  }
}