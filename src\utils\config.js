// 配置管理相关功能
const CONFIG_KEY = 'appConfig'

export class ConfigManager {
  constructor() {
    this.config = {
      // 全局设置
      global: {
        maxConcurrentAccounts: 5,
        autoStart: false,
        minimizeToTray: true,
        startWithSystem: false,
        checkUpdateOnStart: true
      },
      
      // 任务默认配置
      taskDefaults: {
        enterWorkWaitSeconds: 6,
        afterOprWaitSeconds: 4,
        gender: 'all',
        maxLikeCountForDianzan: 999,
        taskConfig: {
          guanzhu: 10,
          dianzan: 1500,
          pinglun: 180,
          shoucan: 150
        }
      },
      
      // 界面设置
      ui: {
        theme: 'light',
        language: 'zh-CN',
        showNotifications: true,
        autoSaveInterval: 30000 // 30秒自动保存
      },
      
      // 安全设置
      security: {
        enableHeartbeat: true,
        heartbeatInterval: 10000,
        maxHeartbeatFailures: 3,
        autoLogoutOnFailure: true
      }
    }
  }

  // 加载配置
  async loadConfig() {
    try {
      const data = await window.ipcRenderer.invoke('get-store', CONFIG_KEY)
      if (data) {
        this.config = this.mergeConfig(this.config, data)
      }
      return this.config
    } catch (error) {
      console.error('加载配置失败', error)
      return this.config
    }
  }

  // 保存配置
  async saveConfig() {
    try {
      await window.ipcRenderer.invoke('set-store', CONFIG_KEY, this.config)
      return true
    } catch (error) {
      console.error('保存配置失败', error)
      return false
    }
  }

  // 合并配置（深度合并）
  mergeConfig(defaultConfig, userConfig) {
    const result = { ...defaultConfig }
    
    for (const key in userConfig) {
      if (userConfig[key] && typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        result[key] = this.mergeConfig(defaultConfig[key] || {}, userConfig[key])
      } else {
        result[key] = userConfig[key]
      }
    }
    
    return result
  }

  // 获取配置项
  get(path) {
    const keys = path.split('.')
    let value = this.config
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        return undefined
      }
    }
    
    return value
  }

  // 设置配置项
  async set(path, value) {
    const keys = path.split('.')
    let current = this.config
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    
    current[keys[keys.length - 1]] = value
    await this.saveConfig()
  }

  // 重置配置
  async resetConfig() {
    this.config = {
      global: {
        maxConcurrentAccounts: 5,
        autoStart: false,
        minimizeToTray: true,
        startWithSystem: false,
        checkUpdateOnStart: true
      },
      taskDefaults: {
        enterWorkWaitSeconds: 6,
        afterOprWaitSeconds: 4,
        gender: 'all',
        maxLikeCountForDianzan: 999,
        taskConfig: {
          guanzhu: 10,
          dianzan: 1500,
          pinglun: 180,
          shoucan: 150
        }
      },
      ui: {
        theme: 'light',
        language: 'zh-CN',
        showNotifications: true,
        autoSaveInterval: 30000
      },
      security: {
        enableHeartbeat: true,
        heartbeatInterval: 10000,
        maxHeartbeatFailures: 3,
        autoLogoutOnFailure: true
      }
    }
    
    await this.saveConfig()
    return this.config
  }

  // 导出配置
  exportConfig() {
    return JSON.stringify(this.config, null, 2)
  }

  // 导入配置
  async importConfig(configJson) {
    try {
      const importedConfig = JSON.parse(configJson)
      this.config = this.mergeConfig(this.config, importedConfig)
      await this.saveConfig()
      return true
    } catch (error) {
      console.error('导入配置失败', error)
      return false
    }
  }

  // 验证配置
  validateConfig(config = this.config) {
    const errors = []
    
    // 验证全局设置
    if (config.global) {
      if (typeof config.global.maxConcurrentAccounts !== 'number' || 
          config.global.maxConcurrentAccounts < 1 || 
          config.global.maxConcurrentAccounts > 20) {
        errors.push('最大并发账号数必须在1-20之间')
      }
    }
    
    // 验证任务默认配置
    if (config.taskDefaults) {
      const { enterWorkWaitSeconds, afterOprWaitSeconds, maxLikeCountForDianzan } = config.taskDefaults
      
      if (typeof enterWorkWaitSeconds !== 'number' || enterWorkWaitSeconds < 1 || enterWorkWaitSeconds > 60) {
        errors.push('进入工作等待时间必须在1-60秒之间')
      }
      
      if (typeof afterOprWaitSeconds !== 'number' || afterOprWaitSeconds < 1 || afterOprWaitSeconds > 60) {
        errors.push('操作后等待时间必须在1-60秒之间')
      }
      
      if (typeof maxLikeCountForDianzan !== 'number' || maxLikeCountForDianzan < 1) {
        errors.push('点赞最大点赞数必须大于0')
      }
    }
    
    return errors
  }

  // 获取任务默认配置
  getTaskDefaults() {
    return { ...this.config.taskDefaults }
  }

  // 更新任务默认配置
  async updateTaskDefaults(newDefaults) {
    this.config.taskDefaults = { ...this.config.taskDefaults, ...newDefaults }
    await this.saveConfig()
  }

  // 获取全局设置
  getGlobalSettings() {
    return { ...this.config.global }
  }

  // 更新全局设置
  async updateGlobalSettings(newSettings) {
    this.config.global = { ...this.config.global, ...newSettings }
    await this.saveConfig()
  }
}

// 工具函数
export const utils = {
  // 格式化时间
  formatTime(date) {
    if (!date) return ''
    const d = new Date(date)
    return d.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  },

  // 格式化持续时间
  formatDuration(ms) {
    if (!ms || ms < 0) return '0秒'
    
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days}天${hours % 24}小时${minutes % 60}分钟`
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`
    } else {
      return `${seconds}秒`
    }
  },

  // 生成随机延时
  randomDelay(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  },

  // 深度克隆对象
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const cloned = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key])
        }
      }
      return cloned
    }
  },

  // 防抖函数
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // 验证抖音ID格式
  validateDouyinId(id) {
    if (!id || typeof id !== 'string') return false
    // 抖音ID通常是字母数字组合，长度在3-20之间
    return /^[a-zA-Z0-9_.-]{3,20}$/.test(id.trim())
  },

  // 验证IP地址格式
  validateIP(ip) {
    if (!ip || typeof ip !== 'string') return false
    // 支持IPv4和域名
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/
    
    return ipv4Regex.test(ip) || domainRegex.test(ip)
  },

  // 验证端口号
  validatePort(port) {
    const portNum = parseInt(port)
    return !isNaN(portNum) && portNum >= 1 && portNum <= 65535
  },

  // 生成UUID
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },

  // 安全的JSON解析
  safeJsonParse(str, defaultValue = null) {
    try {
      return JSON.parse(str)
    } catch (error) {
      console.error('JSON解析失败:', error)
      return defaultValue
    }
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 检查是否为有效的时间格式 HH:MM
  validateTimeFormat(time) {
    if (!time || typeof time !== 'string') return false
    const timeRegex = /^([01]?[0-9]|2[0-4]):([0-5][0-9])$/
    return timeRegex.test(time)
  }
}