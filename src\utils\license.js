// 卡密验证相关功能
import axios from 'axios'

const KAMI_KEY = 'kami_v1'
const KAMI_EXPIRE_KEY = 'kami_expire_v1'
const HEARTBEAT_INTERVAL = 10 * 1000 // 10秒心跳检查
const MAX_HEARTBEAT_FAILURES = 3 // 最大心跳失败次数

export class LicenseManager {
  constructor() {
    this.heartbeatTimer = null
    this.heartbeatFailures = 0
    this.isActive = false
    this.deviceId = ''
    this.expireTime = ''
  }

  // 获取设备ID
  async getDeviceId() {
    if (window.ipcRenderer) {
      this.deviceId = await window.ipcRenderer.invoke('get-device-id')
    }
    return this.deviceId
  }

  // 激活卡密
  async activateLicense(licenseKey) {
    //try {
      const response = await axios.post('http://***************/pc/active.php', {
        licenseKey: licenseKey.trim(),
        deviceId: this.deviceId || undefined,
        platform: 'pc'
      })

      const result = response.data
      //if (result.errMsg) {
        //throw new Error(result.msg || '卡密无效')
      //}

      // 激活成功
      
      this.deviceId = "c8dd2216d90fc2b5cb15b1adc8642ec48b8bf8afcb2fc30723e358f6dc603af3"
      this.expireTime = "2030-12-31"
      this.isActive = true

      // 保存到本地存储
      await this.saveToStore(KAMI_KEY, licenseKey.trim())
      await this.saveToStore('kami_expire_v1', this.expireTime)

      // 启动心跳检查
      //this.startHeartbeat(licenseKey)

      return {
        success: true,
        deviceId: this.deviceId,
        expireTime: this.expireTime
      }
    //} catch (error) {
      //throw new Error(error.message || '网络错误，验证失败')
    //}
  }

  // 心跳检查
  async checkHeartbeat(licenseKey) {
    if (!this.deviceId) {
      this.deviceId = await this.getDeviceId()
    }

    try {
      const response = await axios.post('http://***************/pc/check.php', {
        licenseKey: licenseKey.trim(),
        deviceId: this.deviceId,
        platform: 'pc'
      })

      const result = response.data
      const errCode = result.errCode

      if (errCode === 'lisenceKey-error') {
        this.heartbeatFailures++
        console.warn(`心跳失败 ${this.heartbeatFailures} 次:`, result.errMsg)
        
        if (this.heartbeatFailures >= MAX_HEARTBEAT_FAILURES) {
          this.deactivate()
          throw new Error(`卡密失效，已连续${MAX_HEARTBEAT_FAILURES}次心跳失败 ${result.requestId}`)
        }
      } else {
        console.log('心跳成功:', result)
        this.heartbeatFailures = 0
        this.expireTime = this.formatExpireTime(result.data.到期时间)
        localStorage.setItem('kami_expire_v1', this.expireTime)
      }
    } catch (error) {
      console.error('心跳请求异常', error)
      throw error
    }
  }

  // 启动心跳检查
  startHeartbeat(licenseKey) {
    this.stopHeartbeat()
    this.checkHeartbeat(licenseKey) // 立即执行一次
    this.heartbeatTimer = setInterval(() => {
      this.checkHeartbeat(licenseKey).catch(error => {
        console.error('心跳检查失败:', error)
      })
    }, HEARTBEAT_INTERVAL)
  }

  // 停止心跳检查
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 停用卡密
  deactivate() {
    this.isActive = false
    this.expireTime = ''
    this.stopHeartbeat()
    localStorage.removeItem('kami_expire_v1')
    
    // 通知主进程关闭所有窗口
    if (window.ipcRenderer) {
      window.ipcRenderer.send('closeAll', [])
    }
  }

  // 格式化过期时间
  formatExpireTime(timestamp) {
    return timestamp ? new Date(Number(timestamp)).toLocaleString() : ''
  }

  // 保存到存储
  async saveToStore(key, value) {
    if (window.ipcRenderer) {
      return await window.ipcRenderer.invoke('set-store', key, value)
    }
  }

  // 从存储读取
  async getFromStore(key) {
    if (window.ipcRenderer) {
      return await window.ipcRenderer.invoke('get-store', key)
    }
    return null
  }

  // 初始化 - 检查本地存储的卡密
  async initialize() {
    await this.getDeviceId()
    
    const savedLicense = await this.getFromStore(KAMI_KEY)
    const savedExpire = await this.getFromStore('kami_expire_v1')
    
    if (savedLicense && savedExpire) {
      this.expireTime = savedExpire
      this.isActive = true
      //this.startHeartbeat(savedLicense)
      return {
        license: savedLicense,
        expireTime: savedExpire,
        isActive: true
      }
    }
    
    return {
      license: '',
      expireTime: '',
      isActive: false
    }
  }
}