// 代理IP管理相关功能
const PROXY_IP_LIST_KEY = 'proxyIpList'

export class ProxyManager {
  constructor() {
    this.proxyList = []
  }

  // 加载代理IP列表
  async loadProxyList() {
    try {
      const data = await window.ipcRenderer.invoke('get-store', PROXY_IP_LIST_KEY) || []
      this.proxyList = Array.isArray(data) ? data : []
      return this.proxyList
    } catch (error) {
      console.error('加载代理IP失败', error)
      this.proxyList = []
      return []
    }
  }

  // 保存代理IP列表
  async saveProxyList() {
    try {
      const clonedList = JSON.parse(JSON.stringify(this.proxyList))
      await window.ipcRenderer.invoke('set-store', PROXY_IP_LIST_KEY, clonedList)
    } catch (error) {
      console.error('保存代理IP失败', error)
    }
  }

  // 生成新的IP名称
  generateNewIpName() {
    const existingNumbers = this.proxyList
      .map(proxy => Number(proxy.name.replace('ip', '')))
      .filter(num => !isNaN(num))
    
    const maxNumber = existingNumbers.length ? Math.max(...existingNumbers) : 0
    return `ip${maxNumber + 1}`
  }

  // 验证代理IP配置
  validateProxy(proxyConfig) {
    const { name, ip, port, user, pass, protocol } = proxyConfig

    if (!name) {
      return 'IP名称不能为空'
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(name)) {
      return 'IP名称仅支持字母、数字、下划线和中划线'
    }

    if (!ip) {
      return 'IP不能为空'
    }

    if (!/^([a-zA-Z0-9\-\.]+|\d{1,3}(\.\d{1,3}){3})$/.test(ip)) {
      return 'IP格式不正确'
    }

    if (!port) {
      return '端口不能为空'
    }

    if (!/^\d{1,5}$/.test(port)) {
      return '端口格式不正确'
    }

    if (!['http', 'socks5'].includes(protocol)) {
      return '协议类型错误'
    }

    return true
  }

  // 检查IP名称是否重复
  checkDuplicateName(name, excludeIndex = -1) {
    return this.proxyList.some((proxy, index) => 
      proxy.name === name && index !== excludeIndex
    )
  }

  // 添加代理IP
  async addProxy(proxyConfig) {
    const validation = this.validateProxy(proxyConfig)
    if (validation !== true) {
      throw new Error(validation)
    }

    if (this.checkDuplicateName(proxyConfig.name)) {
      throw new Error('IP名称不能重复')
    }

    this.proxyList.push({ ...proxyConfig })
    await this.saveProxyList()
    return true
  }

  // 更新代理IP
  async updateProxy(index, proxyConfig) {
    if (index < 0 || index >= this.proxyList.length) {
      throw new Error('代理IP不存在')
    }

    const validation = this.validateProxy(proxyConfig)
    if (validation !== true) {
      throw new Error(validation)
    }

    if (this.checkDuplicateName(proxyConfig.name, index)) {
      throw new Error('IP名称不能重复')
    }

    this.proxyList.splice(index, 1, { ...proxyConfig })
    await this.saveProxyList()
    return true
  }

  // 删除代理IP
  async deleteProxy(index) {
    if (index < 0 || index >= this.proxyList.length) {
      throw new Error('代理IP不存在')
    }

    this.proxyList.splice(index, 1)
    await this.saveProxyList()
    return true
  }

  // 批量添加代理IP
  async batchAddProxies(batchContent) {
    const lines = batchContent.split('\n').map(line => line.trim()).filter(Boolean)
    
    if (!lines.length) {
      throw new Error('内容不能为空')
    }

    let maxNumber = Math.max(0, ...this.proxyList.map(proxy => 
      Number(proxy.name.replace('ip', '')) || 0
    ))

    const newProxies = []
    
    for (let line of lines) {
      const [ip, port, user, pass, protocol] = line.split('|').map(item => item?.trim())
      maxNumber++

      const proxyConfig = {
        name: `ip${maxNumber}`,
        ip: ip || '',
        port: port || '',
        user: user || '',
        pass: pass || '',
        protocol: protocol === 'socks5' ? 'socks5' : 'http'
      }

      const validation = this.validateProxy(proxyConfig)
      if (validation !== true) {
        throw new Error(`第${maxNumber}行格式错误：${validation}`)
      }

      newProxies.push(proxyConfig)
    }

    this.proxyList.push(...newProxies)
    await this.saveProxyList()
    return newProxies.length
  }

  // 获取格式化的代理列表（用于选择器）
  getFormattedProxyList() {
    return [
      {
        label: '未代理',
        value: '未代理',
        protocol: ''
      },
      ...this.proxyList.map(proxy => ({
        label: proxy.name,
        value: [proxy.ip, proxy.port, proxy.user, proxy.pass, proxy.protocol].join('|'),
        protocol: proxy.protocol
      }))
    ]
  }

  // 根据值获取代理信息
  getProxyByValue(value) {
    const formattedList = this.getFormattedProxyList()
    return formattedList.find(proxy => proxy.value === value)
  }

  // 创建默认代理配置
  createDefaultProxy() {
    return {
      name: this.generateNewIpName(),
      ip: '',
      port: '',
      user: '',
      pass: '',
      protocol: 'http'
    }
  }
}