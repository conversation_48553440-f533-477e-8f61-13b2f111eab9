// 数据统计相关功能
const STATS_KEY = 'dailyStats'
const HISTORY_KEY = 'statsHistory'

export class StatisticsManager {
  constructor() {
    this.dailyStats = this.createEmptyStats()
    this.history = []
  }

  // 创建空的统计数据
  createEmptyStats() {
    return {
      date: new Date().toDateString(),
      totalAccounts: 0,
      activeAccounts: 0,
      totalLikes: 0,
      totalFollows: 0,
      totalComments: 0,
      totalCollects: 0,
      totalShares: 0,
      totalMessages: 0,
      totalFans: 0,
      accountStats: new Map(),
      startTime: new Date(),
      lastUpdateTime: new Date()
    }
  }

  // 加载统计数据
  async loadStats() {
    try {
      const data = await window.ipcRenderer.invoke('get-store', STATS_KEY)
      if (data && data.date === new Date().toDateString()) {
        this.dailyStats = {
          ...this.createEmptyStats(),
          ...data,
          accountStats: new Map(data.accountStats || [])
        }
      } else {
        // 如果是新的一天，保存昨天的数据到历史记录
        if (data && data.date !== new Date().toDateString()) {
          await this.saveToHistory(data)
        }
        this.dailyStats = this.createEmptyStats()
      }
      
      // 加载历史数据
      const historyData = await window.ipcRenderer.invoke('get-store', HISTORY_KEY)
      this.history = historyData || []
      
      return this.dailyStats
    } catch (error) {
      console.error('加载统计数据失败', error)
      this.dailyStats = this.createEmptyStats()
      return this.dailyStats
    }
  }

  // 保存统计数据
  async saveStats() {
    try {
      const dataToSave = {
        ...this.dailyStats,
        accountStats: Array.from(this.dailyStats.accountStats.entries()),
        lastUpdateTime: new Date()
      }
      await window.ipcRenderer.invoke('set-store', STATS_KEY, dataToSave)
    } catch (error) {
      console.error('保存统计数据失败', error)
    }
  }

  // 保存到历史记录
  async saveToHistory(stats) {
    try {
      const historyItem = {
        ...stats,
        accountStats: Array.from((stats.accountStats || new Map()).entries())
      }
      
      this.history.unshift(historyItem)
      
      // 只保留最近30天的记录
      if (this.history.length > 30) {
        this.history = this.history.slice(0, 30)
      }
      
      await window.ipcRenderer.invoke('set-store', HISTORY_KEY, this.history)
    } catch (error) {
      console.error('保存历史数据失败', error)
    }
  }

  // 更新账号统计
  updateAccountStats(accountId, stats) {
    const currentStats = this.dailyStats.accountStats.get(accountId) || {
      likes: 0,
      follows: 0,
      comments: 0,
      collects: 0,
      shares: 0,
      messages: 0,
      fans: 0,
      lastUpdate: new Date()
    }

    // 更新各项数据
    Object.keys(stats).forEach(key => {
      if (typeof stats[key] === 'number') {
        const increment = stats[key] - (currentStats[key] || 0)
        if (increment > 0) {
          currentStats[key] = stats[key]
          
          // 更新总计
          switch (key) {
            case 'dianzan':
              this.dailyStats.totalLikes += increment
              break
            case 'guanzhu':
              this.dailyStats.totalFollows += increment
              break
            case 'pinglun':
              this.dailyStats.totalComments += increment
              break
            case 'shoucan':
              this.dailyStats.totalCollects += increment
              break
            case 'fenxiang':
              this.dailyStats.totalShares += increment
              break
            case 'sixin':
              this.dailyStats.totalMessages += increment
              break
            case 'fensiNum':
              currentStats.fans = stats[key]
              break
          }
        }
      }
    })

    currentStats.lastUpdate = new Date()
    this.dailyStats.accountStats.set(accountId, currentStats)
    this.dailyStats.lastUpdateTime = new Date()
    
    // 自动保存
    this.saveStats()
  }

  // 更新活跃账号数
  updateActiveAccounts(count) {
    this.dailyStats.activeAccounts = count
    this.saveStats()
  }

  // 更新总账号数
  updateTotalAccounts(count) {
    this.dailyStats.totalAccounts = count
    this.saveStats()
  }

  // 获取今日统计
  getTodayStats() {
    return {
      ...this.dailyStats,
      accountStats: Array.from(this.dailyStats.accountStats.entries())
    }
  }

  // 获取账号统计
  getAccountStats(accountId) {
    return this.dailyStats.accountStats.get(accountId) || {
      likes: 0,
      follows: 0,
      comments: 0,
      collects: 0,
      shares: 0,
      messages: 0,
      fans: 0,
      lastUpdate: null
    }
  }

  // 获取历史统计
  getHistoryStats(days = 7) {
    return this.history.slice(0, days).map(item => ({
      ...item,
      accountStats: new Map(item.accountStats || [])
    }))
  }

  // 获取统计图表数据
  getChartData(days = 7) {
    const history = this.getHistoryStats(days)
    const dates = []
    const likesData = []
    const followsData = []
    const commentsData = []
    const collectsData = []

    // 包含今天的数据
    const allData = [this.dailyStats, ...history].slice(0, days)

    allData.reverse().forEach(item => {
      dates.push(new Date(item.date).toLocaleDateString())
      likesData.push(item.totalLikes || 0)
      followsData.push(item.totalFollows || 0)
      commentsData.push(item.totalComments || 0)
      collectsData.push(item.totalCollects || 0)
    })

    return {
      dates,
      series: [
        { name: '点赞', data: likesData },
        { name: '关注', data: followsData },
        { name: '评论', data: commentsData },
        { name: '收藏', data: collectsData }
      ]
    }
  }

  // 重置今日统计
  async resetTodayStats() {
    // 保存当前数据到历史
    if (this.dailyStats.totalLikes > 0 || this.dailyStats.totalFollows > 0) {
      await this.saveToHistory(this.dailyStats)
    }
    
    // 重置今日数据
    this.dailyStats = this.createEmptyStats()
    await this.saveStats()
  }

  // 导出统计数据
  exportStats(format = 'json') {
    const exportData = {
      today: this.getTodayStats(),
      history: this.history,
      exportTime: new Date().toISOString()
    }

    if (format === 'json') {
      return JSON.stringify(exportData, null, 2)
    } else if (format === 'csv') {
      return this.convertToCSV(exportData)
    }

    return exportData
  }

  // 转换为CSV格式
  convertToCSV(data) {
    const headers = ['日期', '总账号数', '活跃账号数', '点赞总数', '关注总数', '评论总数', '收藏总数']
    const rows = [headers.join(',')]

    // 添加今日数据
    const today = data.today
    rows.push([
      today.date,
      today.totalAccounts,
      today.activeAccounts,
      today.totalLikes,
      today.totalFollows,
      today.totalComments,
      today.totalCollects
    ].join(','))

    // 添加历史数据
    data.history.forEach(item => {
      rows.push([
        item.date,
        item.totalAccounts || 0,
        item.activeAccounts || 0,
        item.totalLikes || 0,
        item.totalFollows || 0,
        item.totalComments || 0,
        item.totalCollects || 0
      ].join(','))
    })

    return rows.join('\n')
  }

  // 计算效率指标
  calculateEfficiency() {
    const stats = this.dailyStats
    const runTime = (new Date() - stats.startTime) / (1000 * 60 * 60) // 小时
    
    return {
      likesPerHour: runTime > 0 ? Math.round(stats.totalLikes / runTime) : 0,
      followsPerHour: runTime > 0 ? Math.round(stats.totalFollows / runTime) : 0,
      commentsPerHour: runTime > 0 ? Math.round(stats.totalComments / runTime) : 0,
      collectsPerHour: runTime > 0 ? Math.round(stats.totalCollects / runTime) : 0,
      totalRunTime: runTime,
      avgPerAccount: stats.activeAccounts > 0 ? {
        likes: Math.round(stats.totalLikes / stats.activeAccounts),
        follows: Math.round(stats.totalFollows / stats.activeAccounts),
        comments: Math.round(stats.totalComments / stats.activeAccounts),
        collects: Math.round(stats.totalCollects / stats.activeAccounts)
      } : { likes: 0, follows: 0, comments: 0, collects: 0 }
    }
  }
}