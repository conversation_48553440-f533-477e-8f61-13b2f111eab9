// 微信通知相关功能
import axios from 'axios'

const WECHAT_CONFIG_KEY = 'wechatConfig'

export class WechatNotifier {
  constructor() {
    this.config = {
      isEnabled: false,
      corpId: '',
      corpSecret: '',
      agentId: '',
      toUser: '@all'
    }
    this.accessToken = ''
    this.tokenExpireTime = 0
  }

  // 加载微信配置
  async loadConfig() {
    try {
      const data = await window.ipcRenderer.invoke('get-store', WECHAT_CONFIG_KEY)
      if (data) {
        this.config = { ...this.config, ...data }
      }
      return this.config
    } catch (error) {
      console.error('加载微信配置失败', error)
      return this.config
    }
  }

  // 保存微信配置
  async saveConfig(config) {
    try {
      this.config = { ...this.config, ...config }
      await window.ipcRenderer.invoke('set-store', WECHAT_CONFIG_KEY, this.config)
      return true
    } catch (error) {
      console.error('保存微信配置失败', error)
      return false
    }
  }

  // 验证配置是否完整
  validateConfig() {
    const { corpId, corpSecret, agentId } = this.config
    return !!(corpId && corpSecret && agentId)
  }

  // 获取访问令牌
  async getAccessToken() {
    const now = Date.now()
    
    // 如果token还未过期，直接返回
    if (this.accessToken && now < this.tokenExpireTime) {
      return this.accessToken
    }

    try {
      const response = await axios.get('https://qyapi.weixin.qq.com/cgi-bin/gettoken', {
        params: {
          corpid: this.config.corpId,
          corpsecret: this.config.corpSecret
        },
        timeout: 10000
      })

      const result = response.data
      if (result.errcode === 0) {
        this.accessToken = result.access_token
        // 提前5分钟过期，避免边界情况
        this.tokenExpireTime = now + (result.expires_in - 300) * 1000
        return this.accessToken
      } else {
        throw new Error(`获取token失败: ${result.errmsg}`)
      }
    } catch (error) {
      console.error('获取微信访问令牌失败', error)
      throw error
    }
  }

  // 发送消息
  async sendMessage(content, msgType = 'text') {
    if (!this.config.isEnabled) {
      console.log('微信通知未启用')
      return false
    }

    if (!this.validateConfig()) {
      console.error('微信配置不完整')
      return false
    }

    try {
      const accessToken = await this.getAccessToken()
      
      const messageData = {
        touser: this.config.toUser,
        msgtype: msgType,
        agentid: parseInt(this.config.agentId)
      }

      if (msgType === 'text') {
        messageData.text = { content }
      } else if (msgType === 'markdown') {
        messageData.markdown = { content }
      }

      const response = await axios.post(
        `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${accessToken}`,
        messageData,
        { timeout: 10000 }
      )

      const result = response.data
      if (result.errcode === 0) {
        console.log('微信消息发送成功')
        return true
      } else {
        console.error('微信消息发送失败:', result.errmsg)
        return false
      }
    } catch (error) {
      console.error('发送微信消息异常', error)
      return false
    }
  }

  // 发送账号状态通知
  async sendAccountStatus(accountId, status, details = '') {
    const content = `账号状态更新\n账号: ${accountId}\n状态: ${status}${details ? `\n详情: ${details}` : ''}\n时间: ${new Date().toLocaleString()}`
    return await this.sendMessage(content)
  }

  // 发送任务完成通知
  async sendTaskComplete(accountId, taskType, count) {
    const content = `任务完成通知\n账号: ${accountId}\n任务: ${taskType}\n完成数量: ${count}\n时间: ${new Date().toLocaleString()}`
    return await this.sendMessage(content)
  }

  // 发送错误通知
  async sendError(accountId, error) {
    const content = `错误通知\n账号: ${accountId}\n错误: ${error}\n时间: ${new Date().toLocaleString()}`
    return await this.sendMessage(content)
  }

  // 发送每日统计
  async sendDailyStats(stats) {
    let content = '每日统计报告\n'
    content += `日期: ${new Date().toLocaleDateString()}\n`
    content += `总账号数: ${stats.totalAccounts}\n`
    content += `活跃账号数: ${stats.activeAccounts}\n`
    content += `点赞总数: ${stats.totalLikes}\n`
    content += `关注总数: ${stats.totalFollows}\n`
    content += `评论总数: ${stats.totalComments}\n`
    content += `收藏总数: ${stats.totalCollects}\n`
    
    return await this.sendMessage(content)
  }

  // 测试连接
  async testConnection() {
    if (!this.validateConfig()) {
      throw new Error('配置信息不完整')
    }

    try {
      await this.getAccessToken()
      const testResult = await this.sendMessage('微信通知测试消息')
      
      if (testResult) {
        return { success: true, message: '测试成功' }
      } else {
        return { success: false, message: '发送测试消息失败' }
      }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  // 启用/禁用通知
  async toggleNotification(enabled) {
    this.config.isEnabled = enabled
    return await this.saveConfig(this.config)
  }
}