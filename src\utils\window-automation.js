/**
 * 抖音窗口自动化脚本模块
 * 从 index.js 提取的完整养号/火力模式自动化操作逻辑
 */

class WindowAutomationScript {
  constructor() {
    this.isRunning = false
    this.isPaused = false
    this.currentTask = null
    this.taskQueue = []
    this.accountId = null
    this.accountData = null
    this.runMode = 0 // 0: 养号模式, 1: 火力模式
    this.runHours = [] // 火力模式运行时段
    this.startTime = "08:00"
    this.endTime = "24:00"
    this.enterWorkWaitSeconds = 6
    this.afterOprWaitSeconds = 4
    this.gender = "all" // all, male, female
    this.maxLikeCountForDianzan = 999
    this.stats = {
      guanzhu: { cur: 0, total: 0 },
      dianzan: { cur: 0, total: 0 },
      pinglun: { cur: 0, total: 0 },
      shoucan: { cur: 0, total: 0 },
      fensiNum: 0,
      jintianFans: 0,
      zuotianFans: 0
    }
    this.lastOprateTime = ""
    this.status = "未打开"
    this.bandDianzanNum = 0
    this.bandShoucanNum = 0
    this.bandPinglunNum = 0
  }

  // 初始化自动化脚本（从 index.js 提取的完整初始化逻辑）
  async init(accountId, accountData = {}) {
    this.accountId = accountId
    this.accountData = accountData

    // 从账号数据中提取配置（与 index.js 数据结构完全一致）
    const options = accountData.options || {}
    this.stats = {
      guanzhu: options.tongjiNum?.guanzhu || { cur: 0, total: 0 },
      dianzan: options.tongjiNum?.dianzan || { cur: 0, total: 0 },
      pinglun: options.tongjiNum?.pinglun || { cur: 0, total: 0 },
      shoucan: options.tongjiNum?.shoucan || { cur: 0, total: 0 },
      fensiNum: options.fensiNum || 0,
      jintianFans: options.jintianFans || 0,
      zuotianFans: options.zuotianFans || 0
    }

    // 运行配置
    this.runMode = accountData.runMode ?? 0
    this.runHours = accountData.runHours || []
    this.startTime = accountData.startTime || "08:00"
    this.endTime = accountData.endTime || "24:00"
    this.enterWorkWaitSeconds = accountData.enterWorkWaitSeconds ?? 6
    this.afterOprWaitSeconds = accountData.afterOprWaitSeconds ?? 4
    this.gender = accountData.gender || "all"
    this.maxLikeCountForDianzan = accountData.maxLikeCountForDianzan ?? 999

    // 状态初始化
    this.status = "未打开"
    this.isPaused = false
    this.bandDianzanNum = 0
    this.bandShoucanNum = 0
    this.bandPinglunNum = 0
    this.lastOprateTime = ""

    console.log(`抖音自动化脚本初始化 - 账号: ${accountId}`, {
      runMode: this.runMode === 1 ? '火力模式' : '养号模式',
      runHours: this.runHours,
      timeRange: `${this.startTime} - ${this.endTime}`,
      stats: this.stats
    })

    // 等待页面完全加载
    await this.waitForPageLoad()

    // 检查运行时间并开始自动化任务
    if (this.isInAllowedTime()) {
      this.startAutomation()
    } else {
      console.log('当前不在允许运行时间段内')
      this.reportStatus('当前不在允许运行时间段')
    }
  }

  // 等待页面加载完成
  async waitForPageLoad() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve()
      } else {
        window.addEventListener('load', resolve, { once: true })
      }
    })
  }

  // 检查是否在允许运行时间内（从 index.js 提取的 $e 函数逻辑）
  isInAllowedTime() {
    const now = new Date()
    const currentHour = now.getHours()

    if (this.runMode === 1) {
      // 火力模式：检查是否在指定时段内
      return this.runHours.includes(currentHour)
    } else {
      // 养号模式：检查时间段
      const [startHour, startMinute] = this.startTime.split(":").map(Number)
      let [endHour, endMinute] = this.endTime.split(":").map(Number)

      if (endHour === 24) endHour = 0

      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const startMinutes = startHour * 60 + startMinute
      const endMinutes = endHour * 60 + endMinute

      if (startMinutes < endMinutes) {
        return currentMinutes >= startMinutes && currentMinutes < endMinutes
      } else {
        return currentMinutes >= startMinutes || currentMinutes < endMinutes
      }
    }
  }

  // 开始自动化任务
  async startAutomation() {
    if (this.isRunning || this.isPaused) return

    this.isRunning = true
    this.status = "运行中"
    console.log(`开始${this.runMode === 1 ? '火力' : '养号'}模式自动化任务`)

    try {
      // 等待进入作品的时间（从 index.js 提取）
      console.log(`等待进入作品 ${this.enterWorkWaitSeconds} 秒...`)
      await this.sleep(this.enterWorkWaitSeconds * 1000)

      // 开始执行任务队列
      await this.executeTaskQueue()
    } catch (error) {
      console.error('自动化任务执行失败:', error)
      this.reportError(error.message)
      this.status = "错误"
    }
  }

  // 执行任务队列（从 index.js 提取的完整任务执行逻辑）
  async executeTaskQueue() {
    const taskOrder = ['guanzhu', 'dianzan', 'pinglun', 'shoucan']
    const taskNames = { guanzhu: '关注', dianzan: '点赞', pinglun: '评论', shoucan: '收藏' }

    for (const taskType of taskOrder) {
      if (!this.isRunning || this.isPaused) break

      const taskConfig = this.stats[taskType]
      if (!taskConfig || taskConfig.total <= 0) continue

      console.log(`开始执行${taskNames[taskType]}任务: ${taskConfig.cur}/${taskConfig.total}`)

      while (taskConfig.cur < taskConfig.total && this.isRunning && !this.isPaused) {
        try {
          // 检查运行时间
          if (!this.isInAllowedTime()) {
            console.log('超出允许运行时间，停止任务')
            this.status = "已停止"
            this.isRunning = false
            this.reportStatus('超出允许运行时间，已停止')
            return
          }

          // 执行具体任务
          const success = await this.executeTask(taskType)

          if (success) {
            taskConfig.cur++
            this.lastOprateTime = new Date().toLocaleString()

            // 更新状态并报告进度
            this.status = "运行中"
            this.reportProgress()

            console.log(`${taskNames[taskType]}完成: ${taskConfig.cur}/${taskConfig.total}`)
          }

          // 操作间隔等待（从 index.js 提取）
          if (taskConfig.cur < taskConfig.total) {
            console.log(`等待 ${this.afterOprWaitSeconds} 秒后继续...`)
            await this.sleep(this.afterOprWaitSeconds * 1000)
          }

        } catch (error) {
          console.error(`执行${taskNames[taskType]}任务失败:`, error)
          await this.sleep(2000) // 错误后短暂等待
        }
      }

      if (taskConfig.cur >= taskConfig.total) {
        console.log(`${taskNames[taskType]}任务完成`)
      }
    }

    // 检查所有任务是否完成
    const allCompleted = taskOrder.every(taskType => {
      const config = this.stats[taskType]
      return !config || config.total <= 0 || config.cur >= config.total
    })

    if (allCompleted) {
      this.status = "已停止"
      this.isRunning = false
      console.log('所有任务完成')
      this.reportCompletion()
    }
  }

  // 执行具体任务（从 index.js 提取的抖音操作逻辑）
  async executeTask(taskType) {
    console.log(`执行任务: ${taskType}`)

    try {
      switch (taskType) {
        case 'guanzhu':
          return await this.performFollow()
        case 'dianzan':
          return await this.performLike()
        case 'pinglun':
          return await this.performComment()
        case 'shoucan':
          return await this.performCollect()
        default:
          return false
      }
    } catch (error) {
      console.error(`执行任务 ${taskType} 异常:`, error)
      return false
    }
  }

  // 执行关注操作（抖音页面关注按钮定位与点击）
  async performFollow() {
    try {
      // 抖音关注按钮选择器（多种可能的选择器）
      const selectors = [
        '[data-e2e="follow-button"]',
        '.follow-button',
        'button[class*="follow"]',
        '.user-card .follow-btn',
        '[data-testid="follow-button"]',
        'button:contains("关注")',
        '.user-info .follow'
      ]

      for (const selector of selectors) {
        const followBtn = document.querySelector(selector)
        if (followBtn && (
          followBtn.textContent.includes('关注') ||
          followBtn.textContent.includes('Follow') ||
          followBtn.getAttribute('aria-label')?.includes('关注')
        )) {
          // 检查按钮是否可点击
          if (!followBtn.disabled && followBtn.offsetParent !== null) {
            followBtn.click()
            await this.sleep(1000)
            console.log('关注操作成功')
            return true
          }
        }
      }

      console.log('未找到可用的关注按钮')
      return false
    } catch (error) {
      console.error('关注操作失败:', error)
      return false
    }
  }

  // 执行点赞操作（抖音页面点赞按钮定位与点击）
  async performLike() {
    try {
      // 检查点赞限制
      if (this.bandDianzanNum >= this.maxLikeCountForDianzan) {
        console.log('已达到点赞上限，跳过点赞')
        return false
      }

      // 抖音点赞按钮选择器
      const selectors = [
        '[data-e2e="like-button"]',
        '.like-button',
        'button[class*="like"]',
        '.video-info .like-btn',
        '[data-testid="like-button"]',
        '.interaction-btn.like',
        '.like-icon'
      ]

      for (const selector of selectors) {
        const likeBtn = document.querySelector(selector)
        if (likeBtn && likeBtn.offsetParent !== null) {
          // 检查是否已经点赞过
          const isLiked = likeBtn.classList.contains('liked') ||
                         likeBtn.classList.contains('active') ||
                         likeBtn.getAttribute('aria-pressed') === 'true'

          if (!isLiked) {
            likeBtn.click()
            this.bandDianzanNum++
            await this.sleep(500)
            console.log(`点赞操作成功 (${this.bandDianzanNum}/${this.maxLikeCountForDianzan})`)
            return true
          }
        }
      }

      console.log('未找到可用的点赞按钮或已点赞')
      return false
    } catch (error) {
      console.error('点赞操作失败:', error)
      return false
    }
  }

  // 执行评论操作（抖音页面评论输入与发送）
  async performComment() {
    try {
      // 检查评论限制
      if (this.bandPinglunNum >= 50) { // 评论限制
        console.log('已达到评论上限，跳过评论')
        return false
      }

      // 抖音评论相关选择器
      const inputSelectors = [
        '[data-e2e="comment-input"]',
        '.comment-input',
        'input[placeholder*="评论"]',
        '.comment-box input',
        '.input-box input',
        'textarea[placeholder*="评论"]'
      ]

      const sendSelectors = [
        '[data-e2e="comment-send"]',
        '.comment-send',
        'button[class*="send"]',
        '.send-btn',
        '.comment-submit',
        'button:contains("发送")'
      ]

      // 查找评论输入框
      let commentInput = null
      for (const selector of inputSelectors) {
        commentInput = document.querySelector(selector)
        if (commentInput && commentInput.offsetParent !== null) break
      }

      if (!commentInput) {
        console.log('未找到评论输入框')
        return false
      }

      // 随机评论内容（根据性别筛选）
      let comments = ['不错', '很好', '赞', '支持', '厉害', '真棒', '学到了', '感谢分享']
      if (this.gender === 'male') {
        comments = ['兄弟牛逼', '学到了', '支持', '真棒', '厉害了']
      } else if (this.gender === 'female') {
        comments = ['好棒呀', '学到了', '太好了', '真不错', '支持']
      }

      const randomComment = comments[Math.floor(Math.random() * comments.length)]

      // 输入评论
      commentInput.focus()
      await this.sleep(200)
      commentInput.value = randomComment

      // 触发输入事件
      const inputEvent = new Event('input', { bubbles: true })
      commentInput.dispatchEvent(inputEvent)

      await this.sleep(500)

      // 查找并点击发送按钮
      let sendBtn = null
      for (const selector of sendSelectors) {
        sendBtn = document.querySelector(selector)
        if (sendBtn && sendBtn.offsetParent !== null && !sendBtn.disabled) break
      }

      if (sendBtn) {
        sendBtn.click()
        this.bandPinglunNum++
        await this.sleep(1000)
        console.log(`评论操作成功: ${randomComment}`)
        return true
      } else {
        console.log('未找到发送按钮')
        return false
      }
    } catch (error) {
      console.error('评论操作失败:', error)
      return false
    }
  }

  // 执行收藏操作（抖音页面收藏按钮定位与点击）
  async performCollect() {
    try {
      // 检查收藏限制
      if (this.bandShoucanNum >= 100) { // 收藏限制
        console.log('已达到收藏上限，跳过收藏')
        return false
      }

      // 抖音收藏按钮选择器
      const selectors = [
        '[data-e2e="collect-button"]',
        '.collect-button',
        'button[class*="collect"]',
        '.video-info .collect-btn',
        '[data-testid="collect-button"]',
        '.interaction-btn.collect',
        '.collect-icon',
        '.favorite-btn'
      ]

      for (const selector of selectors) {
        const collectBtn = document.querySelector(selector)
        if (collectBtn && collectBtn.offsetParent !== null) {
          // 检查是否已经收藏过
          const isCollected = collectBtn.classList.contains('collected') ||
                             collectBtn.classList.contains('active') ||
                             collectBtn.getAttribute('aria-pressed') === 'true'

          if (!isCollected) {
            collectBtn.click()
            this.bandShoucanNum++
            await this.sleep(500)
            console.log(`收藏操作成功 (${this.bandShoucanNum}/100)`)
            return true
          }
        }
      }

      console.log('未找到可用的收藏按钮或已收藏')
      return false
    } catch (error) {
      console.error('收藏操作失败:', error)
      return false
    }
  }

  // 报告进度（与运行详情页交互，从 index.js 提取的数据结构）
  reportProgress() {
    const progress = {
      accountId: this.accountId,
      stats: {
        dianzan: this.stats.dianzan.cur,
        guanzhu: this.stats.guanzhu.cur,
        pinglun: this.stats.pinglun.cur,
        shoucan: this.stats.shoucan.cur,
        fensiNum: this.stats.fensiNum,
        jintianFans: this.stats.jintianFans
      },
      lastOprateTime: this.lastOprateTime,
      status: this.status,
      bandDianzanNum: this.bandDianzanNum,
      bandShoucanNum: this.bandShoucanNum,
      bandPinglunNum: this.bandPinglunNum
    }

    console.log('任务进度更新:', progress)

    // 通过 postMessage 向主进程报告进度（与运行详情页数据结构一致）
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'automation-progress',
        data: progress
      }, '*')
    }

    // 也可以通过 ipcRenderer 直接发送（如果在 Electron 环境中）
    if (window.ipcRenderer) {
      try {
        window.ipcRenderer.send('stats-update', { accountId: this.accountId, stats: progress.stats })
        window.ipcRenderer.send('account-status-update', { accountId: this.accountId, status: this.status })
      } catch (error) {
        console.error('发送进度更新失败:', error)
      }
    }
  }

  // 报告状态变化
  reportStatus(status) {
    this.status = status
    console.log(`状态更新: ${status}`)

    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'automation-status',
        data: { accountId: this.accountId, status }
      }, '*')
    }

    if (window.ipcRenderer) {
      try {
        window.ipcRenderer.send('account-status-update', { accountId: this.accountId, status })
      } catch (error) {
        console.error('发送状态更新失败:', error)
      }
    }
  }

  // 报告错误
  reportError(message) {
    const error = {
      accountId: this.accountId,
      message,
      timestamp: new Date().toISOString()
    }
    
    console.error('自动化错误:', error)
    
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'automation-error',
        data: error
      }, '*')
    }
  }

  // 报告完成
  reportCompletion() {
    const completion = {
      accountId: this.accountId,
      stats: this.stats,
      timestamp: new Date().toISOString()
    }
    
    console.log('自动化任务完成:', completion)
    
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'automation-complete',
        data: completion
      }, '*')
    }
    
    this.isRunning = false
  }

  // 暂停自动化（从 index.js 提取的暂停逻辑）
  pause() {
    this.isPaused = true
    this.status = "已暂停"
    console.log('自动化任务已暂停')
    this.reportStatus('已暂停')
  }

  // 恢复自动化（从 index.js 提取的恢复逻辑）
  resume() {
    if (this.isPaused) {
      this.isPaused = false
      this.status = "运行中"
      console.log('自动化任务已恢复')
      this.reportStatus('运行中')

      // 检查运行时间后继续执行
      if (this.isInAllowedTime()) {
        this.executeTaskQueue()
      } else {
        console.log('当前不在允许运行时间段内，无法恢复')
        this.status = "已停止"
        this.reportStatus('当前不在允许运行时间段')
      }
    }
  }

  // 停止自动化（从 index.js 提取的停止逻辑）
  stop() {
    this.isRunning = false
    this.isPaused = false
    this.status = "已停止"
    this.taskQueue = []
    console.log('自动化任务已停止')
    this.reportStatus('已停止')
  }

  // 工具方法：延时
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WindowAutomationScript
} else if (typeof window !== 'undefined') {
  window.WindowAutomationScript = WindowAutomationScript
}
